<template>
  <v-row justify="center">
    <v-dialog
      v-model="dialog"
      fullscreen
      hide-overlay
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">{{ $t("common_invoices") }} - {{ clientName }}</h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="dialog = false"
          >
            <v-icon> mdi-close </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                class="text-sm-start pt-6 pb-2"
              >
                <h2>
                  <span>{{ $t("common_invoices") }}</span>
                </h2>
              </v-col>
            </v-row>
            <v-row class="align-center mt-0">
              <v-col
                cols="12"
                sm="4"
                md="3"
                class="d-flex align-center"
              >
                <text-search
                  v-model="search"
                  :placeholder="$t('common_search')"
                  class="mb-0"
                  @click:clear="search = null"
                />
              </v-col>
              <v-col
                cols="12"
                sm="4"
                md="3"
              >
                <date-range-picker
                  key="dateRange"
                  ref="dateRange"
                  prepend-icon="mdi-calendar-range"
                  :show-presets="true"
                  :show-custom="true"
                  :disabled="loader"
                  start-preset="currentMonth"
                  @reload-transaction-list="onDateRangeChange"
                />
              </v-col>
              <v-col
                cols="12"
                sm="12"
                md="6"
                class="d-flex justify-end align-center"
              >
                <btn-refresh
                  class="mr-2"
                  @click="getData"
                />
                <export-async-modal
                  btn-class="ml-2"
                  :disabled="loader"
                  :params="exportAsyncParams"
                  :preset="null"
                />
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12">
                <invoices-table
                  :items="dataTable.items"
                  :items-total="dataTable.totalItems"
                  :loader="loader"
                  :sums="{}"
                  @change="onFilteringChange"
                />
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import { debounce } from 'lodash';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import InvoicesTable from '@components/loyalty-cards/invoices/InvoicesTable.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';

export default {
  name: 'ClientInvoicesListModal',
  components: {
    BtnRefresh,
    TextSearch,
    DateRangePicker,
    InvoicesTable,
    ExportAsyncModal,
  },
  props: {
    clientId: {
      type: Number,
      default: null,
      required: false,
    },
    clientName: {
      type: String,
      default: '',
      required: false,
    },
  },
  data() {
    return {
      dialog: false,
      loader: false,
      search: null,
      windowWidth: window.innerWidth,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['createdAt'],
        sortDesc: [true],
      },
      filtering: {
        dates: {
          from: null,
          to: null,
        },
      },
      dateRange: {
        from: null,
        to: null,
      },
      dataTable: {
        items: [],
        totalItems: 0,
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyInvoices3Report',
        startDate: this.dateRange.from
          ? this.$options.filters.formatDateDay(this.dateRange.from)
          : null,
        endDate: this.dateRange.to ? this.$options.filters.formatDateDay(this.dateRange.to) : null,
        search: this.search || null,
        clientId: this.clientId,
      };
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        if (this.$refs.dateRange != null) {
          this.$refs.dateRange.resetPreset();
        }
      } else {
        this.search = '';
        this.pagination.page = 1;
        this.pagination.itemsPerPage = 10;
        this.dataTable.items = [];
      }
    },
    search() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
  },
  created() {
    this.getDataDebounced = debounce(this.getData, 300);
  },
  methods: {
    async getData() {
      this.loader = true;
      this.dataTable.items = []; // Clear items before fetching new ones
      try {
        const params = {
          page: this.pagination?.page || 1,
          perPage: this.pagination?.itemsPerPage || 10,
          clientId: this.clientId,
          search: this.search || null,
        };

        if (this.dateRange.from && this.dateRange.to) {
          params.startDate = this.$options.filters.formatDateDay(this.dateRange.from);
          params.endDate = this.$options.filters.formatDateDay(this.dateRange.to);
        }

        const response = await this.axios.get('/api/loyalty/v3/invoices', { params });

        // Map response data to match InvoicesTable structure
        this.dataTable.items = (response.data.data ?? []).map((item) => ({
          id: item.id,
          createdAt: item.createdAt,
          serviceDate: item.serviceDate,
          number: item.number,
          clientName: item.companyName,
          clientVatId: item.taxNumber,
          priceNet: item.totalNet,
          price: item.total,
          currency: item.currency,
          clientId: this.clientId,
        }));
        this.dataTable.totalItems = response.data.total ?? 0;
      } catch (error) {
        this.dataTable.items = [];
        this.dataTable.totalItems = 0;
      } finally {
        this.loader = false;
      }
    },
    onDateRangeChange(dates) {
      this.dateRange = dates;
      this.getData();
    },
    onFilteringChange(options) {
      this.pagination = options;
      this.getData();
    },
    show() {
      this.dialog = true;
    },
  },
};
</script>
