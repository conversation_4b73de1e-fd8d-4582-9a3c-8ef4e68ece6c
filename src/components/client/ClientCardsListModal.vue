<template>
  <v-row justify="center">
    <v-dialog
      v-model="dialog"
      fullscreen
      hide-overlay
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('dashboard_heading') }} - {{ clientName }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="dialog = false"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid>
            <v-row>
              <v-col
                cols="12"
                class="text-sm-start pt-6 pb-2"
              >
                <h2>
                  <span>{{ $t('loyaltyCards_cardsList') }}</span>
                </h2>
              </v-col>
            </v-row>
            <v-row class="align-center mt-0">
              <v-col
                cols="12"
                sm="6"
                class="d-flex align-center"
              >
                <text-search
                  v-model="search"
                  class="mb-0"
                />
              </v-col>
              <v-col
                cols="12"
                sm="6"
                class="d-flex justify-end align-center"
              >
                <btn-refresh
                  class="mr-2"
                  @click="getData"
                />
                <v-btn
                  small
                  color="primary"
                  @click="$refs.addCardModal.show()"
                >
                  <v-icon left>
                    mdi-plus
                  </v-icon>
                  {{ $t('actions.add_key') }}
                </v-btn>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12">
                <v-data-table
                  :headers="headers"
                  :items="dataTable.items"
                  :loading="loader"
                  :options.sync="pagination"
                  :footer-props="footerProps"
                  mobile-breakpoint="0"
                  :server-items-length="dataTable.totalItems"
                  item-key="number"
                >
                  <template #item="{ item, index }">
                    <tr :key="`row-${item.number}-${index}`">
                      <td class="text-sm-start">
                        <div class="card-container mt-2">
                          <div class="card-number">
                            {{ item.number.toUpperCase() }}
                          </div>
                          <v-tooltip
                            key="notice"
                            bottom
                            class="notice-icon"
                          >
                            <template #activator="{ on, attrs }">
                              <div
                                v-bind="attrs"
                                v-on="on"
                              >
                                <v-icon
                                  v-if="item.additInfo != null"
                                  color="yellow darken-2"
                                >
                                  mdi-alert
                                </v-icon>
                              </div>
                            </template>
                            <span>{{ item.additInfo }}</span>
                          </v-tooltip>
                          <div class="lock-icon">
                            <v-icon
                              v-if="item.status === 'BLOCKED'"
                              color="grey darken-2"
                            >
                              mdi-lock
                            </v-icon>
                          </div>
                          <v-tooltip
                            :key="`virtual${item.cardNumber}`"
                            bottom
                          >
                            <template #activator="{ on, attrs }">
                              <div
                                class="notice-icon"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <v-icon
                                  v-if="item.cardType === 'VIRTUAL'"
                                >
                                  mdi-web
                                </v-icon>
                              </div>
                            </template>
                            <span>{{ $t('loyaltyCards_tableVirtualCard') }}</span>
                          </v-tooltip>
                          <p class="hidden-md-and-up m-0 cardName card-name">
                            {{ item.alias }}
                          </p>
                        </div>
                      </td>
                      <td class="hidden-sm-and-down md-and-up text-sm-start">
                        {{ item.alias }}
                      </td>
                      <td class="hidden-sm-and-down md-and-up text-sm-start">
                        {{ item.email }}
                      </td>
                      <td class="hidden-sm-and-down md-and-up text-sm-start border-right">
                        <template v-if="item.lastContact !== null">
                          {{ item.lastContact|formatDateDayTime }}
                        </template>
                        <template v-else>
                          &mdash;
                        </template>
                      </td>
                      <td class="hidden-sm-and-down md-and-up text-sm-end">
                        {{ item.topUps|currencySymbol(item.currencySymbol) }}
                      </td>
                      <td class="hidden-sm-and-down md-and-up text-sm-end">
                        {{ item.payments|currencySymbol(item.currencySymbol) }}
                      </td>
                      <td class="text-sm-end border-right">
                        <v-tooltip bottom>
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              {{ item.balance|currencySymbol(item.currencySymbol) }}
                            </div>
                            <div v-if="item.toSend && item.toSend > 0">
                              (+{{ item.toSend|currencySymbol(item.currencySymbol) }}
                            </div>
                          </template>
                          <span>{{ $t('loyaltyCards_cardFundsTooltip') }}</span>
                        </v-tooltip>
                      </td>

                      <td class="d-flex justify-end">
                        <div class="d-flex align-center justify-end">
                          <v-tooltip bottom>
                            <template #activator="{ on, attrs }">
                              <v-btn
                                :key="`edit-${item.number}`"
                                class="card-button mr-2"
                                elevation="1"
                                v-bind="attrs"
                                tile
                                rounded
                                x-small
                                fab
                                color="secondary"
                                v-on="on"
                                @click.stop
                                @click.native="openModal(
                                  `editCardDialog${item.number}`,
                                  {cardNumber: item.number}
                                )"
                              >
                                <v-icon>mdi-pencil</v-icon>
                              </v-btn>
                            </template>
                            <span>{{ $t('actions.edit') }}</span>
                          </v-tooltip>
                          <v-tooltip bottom>
                            <template #activator="{ on, attrs }">
                              <v-btn
                                :key="`topup-${item.number}`"
                                class="card-button"
                                elevation="1"
                                v-bind="attrs"
                                tile
                                rounded
                                x-small
                                fab
                                color="primary"
                                v-on="on"
                                @click.stop
                                @click.native="openModal(
                                  `topUpCardDialog${item.number}`,
                                  {cardNumber: item.number}
                                )"
                              >
                                <v-icon>mdi-trending-up</v-icon>
                              </v-btn>
                            </template>
                            <span>{{ $t('actions.refill_card') }}</span>
                          </v-tooltip>
                        </div>
                        <div>
                          <card-edit-modal
                            :key="`edit-modal-${item.number}`"
                            :ref="`editCardDialog${item.number}`"
                            :card-number="item.number"
                            :card-token="item.cardToken"
                            @reload-card-list="getData"
                          />
                          <card-top-up-modal
                            :key="`topup-modal-${item.number}`"
                            :ref="`topUpCardDialog${item.number}`"
                            :card-number="item.number"
                            :card-token="item.cardToken"
                            :client-id="item.clientId ? item.clientId : null"
                            @reload-card-list="getData"
                          />
                        </div>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
    <add-client-card-modal
      ref="addCardModal"
      :client-id="clientId"
      @reload-card-list="getData"
    />
  </v-row>
</template>

<script>
import CardTopUpModal from '@components/loyalty-cards/CardTopUpModal.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import { debounce } from 'lodash';
import BtnRefresh from '../common/button/BtnRefresh.vue';
import AddClientCardModal from './AddClientCardModal.vue';
import CardEditModal from '../loyalty-cards/CardEditModal.vue';

export default {
  name: 'ClientCardsListModal',
  components: {
    BtnRefresh,
    AddClientCardModal,
    CardEditModal,
    CardTopUpModal,
    TextSearch,
  },
  props: {
    clientId: {
      type: [Number],
      required: true,
    },
    clientName: {
      type: [String],
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loader: false,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['ctime'],
        sortDesc: [true],
      },
      footerProps: {
        'items-per-page-options': [10, 25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [25, 50, 100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        items: [],
        totalItems: 0,
      },
      search: '',
    };
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('loyaltyCards_card'),
          value: 'number',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_name'),
          value: 'alias',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('common_email'),
          value: 'email',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('common_lastUsage'),
          value: 'mtime',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('common_topups'),
          value: 'topUps',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          align: 'right',
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_payments'),
          value: 'payments',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          align: 'right',
          sortable: false,
        },
        {
          text: `${this.$t('loyaltyCards_cardFunds')} (${this.$t('loyaltyCards_toSend')})`,
          value: 'balance',
          align: 'right',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          sortable: false,
          align: 'right',
        },
      ];
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        this.dataTable.items = [];
        this.getData();
      } else {
        this.search = '';
        this.pagination.page = 1;
        this.pagination.itemsPerPage = 10;
        this.dataTable.items = [];
      }
    },
    pagination: {
      handler(newValue, oldValue) {
        if (
          oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
        ) {
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    search() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
  },
  created() {
    this.getDataDebounced = debounce(this.getData, 400);
  },

  methods: {
    async getData() {
      this.loader = true;
      this.dataTable.items = []; // Clear items before fetching new ones
      try {
        const params = {
          page: this.pagination.page,
          perPage: this.pagination.itemsPerPage,
          clientId: this.clientId,
          search: this.search || null,
        };
        const response = await this.axios.get('/api/loyalty/v3/cards', {
          params,
        });
        if (response.status === 200 && response.data) {
          // Create new array reference to trigger reactivity
          this.dataTable.items = [...(response.data.data ?? [])];
          this.dataTable.totalItems = response.data.total ?? 0;
        }
      } catch (error) {
        this.dataTable.items = [];
        this.dataTable.totalItems = 0;
      } finally {
        this.loader = false;
      }
    },
    show() {
      this.dialog = true;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>

<style lang="css" scoped>
.card-container {
  display: flex;
  align-items: center;
}

.card-number {
  font-weight: bold;
}
</style>
