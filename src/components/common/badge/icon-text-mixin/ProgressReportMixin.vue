<script>
export default {
  name: 'ProgressReportMixin',
  data() {
    return {
      icons: {
        ERROR: 'mdi-progress-alert',
        NEW: 'mdi-progress-star',
        PROCESS: 'mdi-progress-clock',
        DONE: 'mdi-check-circle-outline',
        DEFAULT: 'mdi-help-circle-outline',
      },
      colors: {
        ERROR: 'error',
        NEW: 'gray',
        PROCESS: 'progress',
        DONE: 'green darken-2',
        DEFAULT: 'gray',
      },
      texts: {
        ERROR: this.$t('common_progressError'),
        NEW: this.$t('common_progressNew'),
        PROCESS: this.$t('common_process'),
        DONE: this.$t('common_done'),
      },
    };
  },
  methods: {
    getReportProgressIcon(progress) {
      if (progress in this.icons) {
        return {
          icon: this.icons[progress],
          color: this.colors[progress],
          text: this.texts[progress],
        };
      }

      return {
        icon: this.icons.DEFAULT,
        color: this.colors.DEFAULT,
        text: progress,
      };
    },
  },
};
</script>
