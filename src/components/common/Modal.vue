<!--
Generyczny komponent Modal - Self Contained

Przyk<PERSON><PERSON> użycia:

1. Podstawowy modal z formularzem:
<GenericModal
  ref="userModal"
  title="Dodaj użytkownika"
  @submit="handleSubmit"
>
  <v-text-field
    v-model="user.name"
    label="Nazwa"
    required
  />
  <v-text-field
    v-model="user.email"
    label="Email"
    required
  />
</GenericModal>

// W metodach komponentu:
methods: {
  openUserModal() {
    this.$refs.userModal.openDialog();
  },
  handleSubmit() {
    // logika zapisu
    this.$refs.userModal.closeDialog(); // zamknij po sukcesie
  }
}

2. Modal bez formularza (tylko zawart<PERSON>ć):
<GenericModal
  ref="infoModal"
  title="Informacje"
  :has-form="false"
  :show-confirm-button="false"
  cancel-button-text="Zamknij"
>
  <p>Treść informacyjna...</p>
</GenericModal>

3. Modal z niestandardowymi przyciskami:
<GenericModal
  ref="confirmModal"
  title="Potwierdź akcję"
  confirm-button-text="Zatwierdź"
  cancel-button-text="Anuluj"
  :loading="isLoading"
  @submit="handleConfirm"
>
  <template #actions>
    <v-btn @click="$refs.confirmModal.closeDialog()">Anuluj</v-btn>
    <v-btn color="warning" @click="handleDelete">Usuń</v-btn>
    <v-btn color="primary" @click="submit">Zapisz</v-btn>
  </template>
</GenericModal>

Props:
- title (required): Tytuł modala
- confirmButtonText: Tekst przycisku zatwierdzającego (domyślnie "Zapisz")
- cancelButtonText: Tekst przycisku anulującego (domyślnie "Anuluj")
- loading: Stan ładowania (blokuje interakcję)
- maxWidth: Maksymalna szerokość modala (domyślnie "800")
- fullscreen: Czy modal ma być pełnoekranowy
- scrollable: Czy zawartość ma być przewijalna
- hasForm: Czy zawartość zawiera formularz (domyślnie true)
- showActions: Czy pokazywać sekcję przycisków (domyślnie true)
- showConfirmButton: Czy pokazywać przycisk zatwierdzający (domyślnie true)
- autoClearForm: Czy automatycznie czyścić formularz przy zamknięciu (domyślnie true)

Metody publiczne:
- openDialog(): Otwiera modal
- closeDialog(): Zamyka modal

Events:
- submit: Emitowany po kliknięciu przycisku zatwierdzającego i pomyślnej walidacji
- beforeSubmit: Emitowany przed walidacją
- close: Emitowany przy zamknięciu modala
- beforeClose: Emitowany przed zamknięciem modala

Sloty:
- default: Główna zawartość modala
- title: Niestandardowy tytuł (zastępuje domyślny)
- actions: Niestandardowe przyciski akcji
- before: Zawartość przed modalem
-->

<template>
  <div>
    <slot name="before" />
    <v-dialog
      v-model="dialog"
      :max-width="maxWidth"
      :persistent="loader"
      :fullscreen="fullscreenInternal"
      :scrollable="scrollable"
    >
      <v-card :loading="loader">
        <v-card-title class="title">
          <slot name="title">
            <span class="headline">
              <h5 class="text-uppercase">{{ title }}</h5>
            </span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click="closeDialog"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <v-form
            v-if="hasForm"
            ref="form"
            @submit="submit"
          >
            <slot />
          </v-form>
          <template v-else>
            <slot />
          </template>
        </v-card-text>
        <v-card-actions v-if="showActions">
          <slot name="actions">
            <v-spacer />
            <v-btn
              color="gray"
              text
              :disabled="loader"
              @click="closeDialog"
            >
              {{ cancelButtonText || $t('actions.cancel') }}
            </v-btn>
            <v-btn
              v-if="showConfirmButton"
              color="primary"
              :loading="loading"
              :disabled="loader"
              @click="submit"
            >
              {{ confirmButtonText || $t('actions.save') }}
            </v-btn>
          </slot>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'GenericModal',
  props: {
    title: {
      type: String,
      required: true,
    },
    confirmButtonText: {
      type: String,
      default: null,
    },
    cancelButtonText: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    maxWidth: {
      type: [String, Number],
      default: '700',
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    scrollable: {
      type: Boolean,
      default: false,
    },
    hasForm: {
      type: Boolean,
      default: true,
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    showConfirmButton: {
      type: Boolean,
      default: true,
    },
    autoClearForm: {
      type: Boolean,
      default: true,
    },
  },
  data: () => ({
    dialog: false,
    loadingInternal: false,
  }),
  computed: {
    fullscreenInternal() {
      return this.fullscreen || this.$vuetify.breakpoint.smAndDown;
    },
    loader() {
      return this.loading || this.loadingInternal;
    },
  },
  methods: {
    validate() {
      if (this.hasForm && this.$refs.form) {
        return this.$refs.form.validate();
      }
      return true;
    },
    clear() {
      if (this.hasForm && this.$refs.form) {
        this.$refs.form.reset();
        this.$refs.form.resetValidation();
      }
    },
    beforeSubmit() {
      this.$emit('beforeSubmit');
    },
    submit() {
      this.beforeSubmit();
      if (this.validate()) {
        this.$emit('submit');
      }
    },
    openDialog() {
      this.dialog = true;
      this.$emit('open');
    },
    closeDialog() {
      if (this.autoClearForm) {
        this.$nextTick(() => {
          this.clear();
        });
      }
      this.$emit('beforeClose');
      this.dialog = false;
      this.$emit('close');
    },
  },
};
</script>
