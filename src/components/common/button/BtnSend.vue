<template>
  <v-btn
    color="primary"
    :loading="loading"
    :disabled="disabled"
    @click.native="$emit('click')"
  >
    <template v-if="text">
      {{ text }}
    </template>
    <template v-else>
      {{ $t('actions.add') }}
    </template>
    <v-icon
      small
      right
    >
      mdi-send
    </v-icon>
  </v-btn>
</template>

<script>
export default {
  name: 'BtnSend',
  props: {
    text: {
      type: String,
      default: null,
      nullable: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
