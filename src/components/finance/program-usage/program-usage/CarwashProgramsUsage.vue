<template>
  <v-container
    fluid
    class="p-0"
  >
    <v-row>
      <carwash-and-date-range-filter
        :carwashes="carwashes"
        :disabled="loader"
        settings-namespace="finance:dates"
        carwash-settings-namespace="finance:carwashes"
        @change="onFiltersChange"
      />
    </v-row>
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <heading
          :dates="filtering.dates"
        />
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-refresh
          class="mr-2"
          :disabled="loader"
          @click="getData"
        />
        <export-async-modal
          :params="getParams('v2\\FinanceProgramsTotal')"
          :disabled="loader"
          :preset="filtering.dates.value"
        />
      </v-col>
    </v-row>
    <v-row class="mb-5">
      <v-col
        cols="12"
        lg="8"
      >
        <programs-usage-bar-chart
          :values="itemsDaily"
          :loader="loader"
          :filtering="filtering"
        />
      </v-col>
      <v-col
        cols="12"
        lg="4"
      >
        <programs-usage-pie-chart
          :programs="sums"
          :loader="loader"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        class="pt-0"
      >
        <programs-usage-total-table
          :items="items"
          :sums="sums"
          :loader="loader"
          :filtering="filtering"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import CarwashAndDateRangeFilter from '@components/common/filters/CarwashAndDateRangeFilter.vue';
import DateOptionsMixins from '@components/common/mixins/DateOptionsMixins.vue';
import { mapGetters } from 'vuex';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import ProgramsUsageTotalTable from './ProgramsUsageTotalTable.vue';
import ProgramsUsageBarChart from '../ProgramsUsageBarChart.vue';
import ProgramsUsagePieChart from './ProgramsUsagePieChart.vue';
import Heading from '../Heading.vue';

export default {
  name: 'CarwashProgramsUsage',
  components: {
    ExportAsyncModal,
    ProgramsUsageTotalTable,
    ProgramsUsageBarChart,
    ProgramsUsagePieChart,
    CarwashAndDateRangeFilter,
    Heading,
    BtnRefresh,
  },
  mixins: [
    DateOptionsMixins,
  ],
  data() {
    return {
      settingsNamespace: 'finance:dates',
      itemsDaily: [],
      loader: true,
      loaderTotal: true,
      sums: {},
      items: [],
      filtering: {
        carwash: null,
        dates: {
          from: null,
          to: null,
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
  },
  mounted() {},
  methods: {

    async getData() {
      this.items = [];
      this.itemsDaily = [];
      this.sums = {};

      this.loader = true;
      await Promise.all([
        this.getTotalUsage(),
        this.getDailyUsage(),
      ]).then(
        () => {
          this.loader = false;
        },
      );
    },
    onFiltersChange(filtering) {
      this.filtering = {
        ...this.filtering,
        ...filtering,
      };
      this.getData();
    },
    getTotalUsage() {
      return this.getReport('v2\\FinanceProgramsTotal')
        .then(
          (response) => {
            if (response.status === 200) {
              this.items = response.data.data;
              this.sums = response.data.totalSum;
            }
          },
        );
    },
    getDailyUsage() {
      return this.getReport('v2\\FinanceProgramsUsageDaily')
        .then(
          (response) => {
            if (response.status === 200) {
              this.itemsDaily = response.data.data;
            }
          },
        );
    },
    getReport(reportName) {
      return this.axios.get('/api/reports/data', {
        params: this.getParams(reportName),
      });
    },
    getParams(reportName) {
      return {
        serial: this.filtering.carwash,
        startDate: this.filtering.dates.from,
        endDate: this.filtering.dates.to,
        report: reportName,
      };
    },
  },
};
</script>
