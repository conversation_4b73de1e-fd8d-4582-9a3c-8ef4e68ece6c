<template>
  <v-container fluid>
    <v-row>
      <v-col
        cols="12"
        sm="6"
        md="6"
      >
        <carwash-filter
          :carwashes="filteringOptions.carwash.carwashes"
          :disabled="loader"
          @change="onFiltersChange"
        />
      </v-col>
      <v-col
        cols="12"
        md="6"
        sm="6"
        class="d-flex justify-end align-center"
      >
        <btn-refresh
          class="ml-2"
          :disabled="loader"
          @click="getData"
        />
        <export-async-modal
          btn-class="ml-2"
          :params="exportAsyncParams"
          :disabled="loader"
          :show-dates="false"
        />
      </v-col>
    </v-row>

    <dosage-table
      :items="loader === true ? [] : items"
      :loader="loader"
    />
  </v-container>
</template>

<script>

import DosageDataFetchMixin from '@components/finance/program-usage/mixins/DosageDataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import CarwashFilter from '@components/common/filters/CarwashFilter.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import DosageTable from '@components/finance/program-usage/dosage/DosageTable.vue';

export default {
  name: 'DosageTab',
  components: {
    CarwashFilter,
    BtnRefresh,
    ExportAsyncModal,
    DosageTable,
  },
  mixins: [
    DosageDataFetchMixin,
    FiltersHandlingMixin,
  ],
  data() {
    return {
      dataUrl: '/api/reports/data',
    };
  },
  computed: {
    exportAsyncParams() {
      const carwash = this.filtering.carwash || null;
      return {
        serial: carwash,
        report: 'v2\\FinanceUsageDosage',
      };
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    parseApiResponseData(data) {
      this.items = data;
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash,
          report: 'v2\\FinanceUsageDosage',
        },
      };
    },
  },
};
</script>
