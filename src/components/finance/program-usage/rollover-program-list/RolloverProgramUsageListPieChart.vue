<template>
  <div>
    <export-async-modal
      btn-class="float-right"
      :params="exportAsyncParams"
      :disabled="loader"
      :preset="filtering.dates.value"
    />
    <pie-chart
      class="px-2"
      :title="$t('finance_title')"
      :values="getValues"
      :loader="loader"
      :unit="$t('programsusage.table.wash')"
    />
  </div>
</template>

<script>
import PieChart from '@components/common/charts/PieChart.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';

export default {
  name: 'RolloverProgramsUsagePieChart',
  components: {
    ExportAsyncModal,
    PieChart,
  },
  props: {
    loader: {
      type: Boolean,
      default: false,
    },
    values: {
      type: Array,
      default() {
        return [];
      },
    },
    filtering: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      colors: [
        '#3B81E3',
        '#2A5DA3',
        '#a4bfe8',
        '#5E7DA8',
        '#81A9E6',
        '#133c77',
        '#9EA8B8',
        '#585D66',
        '#10233D',
      ],
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        serial: this.filtering.carwash,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        report: 'v2\\FinancePortalProgramsUsageTotal',
      };
    },
    getValues() {
      const carwashes = this.values.filter((carwash) => carwash.total > 0);
      const programs = {};
      carwashes.forEach((cw) => {
        Object.entries(cw.programs).forEach(([programName, program]) => {
          if (program && !(programName in programs)) {
            programs[programName] = 0;
          }
          programs[programName] += program;
        });
      });
      return Object.entries(programs).filter(([, value]) => !!value).map(([key, value], index) => ({
        value,
        name: `${this.$t('programsusage.table.program')} ${key.replace('p', '')}`,
        itemStyle: {
          color: this.colors[index],
        },
      }));
    },
  },
};
</script>
