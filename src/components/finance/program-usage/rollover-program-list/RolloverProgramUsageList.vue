<template>
  <v-container
    fluid
    class="p-0"
  >
    <v-row>
      <type-carwash-and-date-range-filter
        :carwashes="filteringOptions.carwash.rollovers"
        :disabled="loader"
        settings-namespace="finance:dates"
        carwash-settings-namespace="finance:carwashes"
        @change="onFiltersChange"
      />
    </v-row>
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <heading
          :dates="filtering.dates"
        />
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-refresh
          class="mr-2"
          :disabled="loader"
          @click="getData"
        />
        <export-async-modal
          btn-class="ml-2"
          :preset="filtering.dates.value"
          :params="exportAsyncParams"
          :disabled="loader"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
      >
        <text-search
          v-model="search"
          :disabled="loader"
          @input="getDataDebounced"
        />
      </v-col>
    </v-row>
    <v-row class="mb-5">
      <v-col
        cols="12"
      >
        <rollover-program-usage-list-total-table
          :items="itemsList"
          :sums="sums"
          :loader="loader"
          :filtering="filtering"
          :items-total="totalItems"
          :options="filtering.options"
          @change="onFiltersChange"
          @reload-list="getData"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import debounce from 'lodash/debounce';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import RolloverProgramUsageListTotalTable from '@components/finance/program-usage/rollover-program-list/RolloverProgramUsageListTotalTable.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import TypeCarwashAndDateRangeFilter from './TypeCarwashAndDateRangeFilter.vue';
// import ProgramsUsageBarChart from '../ProgramsUsageBarChart.vue';
// import RolloverProgramsUsagePieChart from './RolloverProgramUsageListPieChart.vue';
import Heading from '../Heading.vue';
// import ProgramsUsageOverTimeBarChart from '../program-usage/ProgramsUsageOverTimeBarChart.vue';

export default {
  name: 'RolloverProgramsUsage',
  components: {
    ExportAsyncModal,
    TextSearch,
    RolloverProgramUsageListTotalTable,
    // ProgramsUsageBarChart,
    // RolloverProgramsUsagePieChart,
    TypeCarwashAndDateRangeFilter,
    Heading,
    BtnRefresh,
    // ProgramsUsageOverTimeBarChart,
  },
  mixins: [
    DataFetchMixin,
    FiltersHandlingMixin,
  ],
  data() {
    return {
      search: null,
      settingsNamespace: 'finance:dates',
      dataUrl: '/api/reports/data',
      itemsDaily: [],
      itemsList: [],
      filtering: {
        options: {
          sortBy: ['date'],
          sortDesc: [true],
          itemsPerPage: 25,
        },
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        serial: this.filtering.carwash,
        page: this.filtering.options.page || null,
        perPage: this.filtering.options.itemsPerPage || null,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        report: 'v2\\FinancePortalProgramUsageList',
        search: this.search,
        status: this.filtering.status,
      };
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getData();
    }, 1000);
  },
  methods: {
    parseApiResponseData(data) {
      this.itemsList = data.map((item) => ({
        ...item,
      }));
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash,
          page: this.filtering.options.page || null,
          perPage: this.filtering.options.itemsPerPage || null,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          report: 'v2\\FinancePortalProgramUsageList',
          search: this.search,
          status: this.filtering.status,
        },
      };
    },
  },
};
</script>
