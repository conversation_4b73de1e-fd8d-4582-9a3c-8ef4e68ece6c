<template>
  <v-card
    outlined
    color="blue-grey lighten-5"
  >
    <v-card-title class="px-2 py-2">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="6"
          class="text-sm-start"
        >
          {{ $t('finance_details') }}
        </v-col>
        <v-col
          cols="6"
          class="d-flex justify-end"
        >
          <export-async-modal
            btn-class="ml-2"
            :show-dates="false"
            :params="exportAsyncParams"
          />
        </v-col>
      </v-layout>
    </v-card-title>

    <v-card-text>
      <v-data-table
        dense
        :headers="headers"
        :items="items"
        hide-default-footer
        class="elevation-2"
        item-key="name"
        mobile-breakpoint="0"
        :sort-by="['device', 'stand']"
        :items-per-page="-1"
      >
        <template #item="{ item }">
          <tr>
            <td class="font-weight-bold text-sm-start">
              <v-icon
                small
                color="grey lighten-1"
                class="d-inline-block mr-1"
              >
                {{ item.icon }}
              </v-icon>
              {{ item.name }}
            </td>
            <td>{{ item.resetTime | formatDateDayTimeWithSeconds }}</td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.coins"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.bills"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.tokens"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.sumRow"
              weight="bold"
            />
          </tr>
        </template>

        <!-- Summary row -->
        <template #[`body.append`]>
          <tr class="table-summary">
            <td
              class="text-start font-weight-bold"
              colspan="2"
            >
              {{ $t('turnover.table.total') }}
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="sums.coins"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="sums.bills"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="sums.tokens"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="sums.all"
              weight="bold"
            />
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';

export default {
  name: 'MoneyCollectStandsDetailsTable',
  components: {
    CustomCurrencySymbolCell,
    ExportAsyncModal,
  },
  props: {
    parent: {
      type: Object,
      required: true,
    },
    sums: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      headers: [
        {
          value: 'name',
          text: this.$t('turnover.table.name'),
        },
        {
          value: 'date',
          text: this.$t('turnover.table.date'),
        },
        {
          value: 'coin',
          text: this.$t('turnover.table.coins'),
          align: 'end',
        },
        {
          value: 'bill',
          text: this.$t('turnover.table.banknotes'),
          align: 'end',
        },
        {
          value: 'token',
          text: this.$t('turnover.table.tokens'),
          align: 'end',
        },
        {
          value: 'sum',
          text: this.$t('turnover.table.sum'),
          align: 'end',
        },
      ],
      deviceIcons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
      },
    };
  },
  computed: {
    items() {
      const { content } = this.parent;
      return content.map((stand) => ({
        name: stand.deviceName,
        icon: this.getIconForDeviceType(stand.deviceType),
        date: stand.resetTime,
        ...stand,
      }));
    },
    exportAsyncParams() {
      return {
        id: this.parent.id,
        report: 'v2\\FinanceMoneyCollects',
        sources: 'CAR_WASH',
      };
    },
  },
  methods: {
    getIconForDeviceType(deviceType) {
      if (deviceType in this.deviceIcons) {
        return this.deviceIcons[deviceType];
      }
      return 'mdi-help';
    },
  },
};

</script>
