<template>
  <v-card
    v-if="items.length"
    outlined
    color="blue-grey lighten-5"
  >
    <v-card-title class="px-2 py-2">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="6"
          class="text-sm-start"
        >
          {{ $t('finance_yeti') }}
        </v-col>
        <v-col
          cols="6"
          class="d-flex justify-end"
        >
          <export-async-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
          />
        </v-col>
      </v-layout>
    </v-card-title>
    <v-card-text>
      <v-data-table
        dense
        :headers="headers"
        :items="items"
        hide-default-footer
        class="elevation-2"
        mobile-breakpoint="0"
        disable-sort
      >
        <template #item="{ item: yeti }">
          <tr>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="yeti.coin"
              additional-class="border-right"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="yeti.hopperA"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="yeti.hopperB"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="yeti.safe"
              align="center"
            />
          </tr>
          <tr class="text-center">
            <td
              class="border-right"
            >
              <span>{{ $t('finance_sumSucked') }}: </span>
              <strong>{{ yeti.sumSucked|currencySymbol(currencySymbol) }}</strong>
            </td>
            <td
              :colspan="3"
            >
              <span>{{ $t('finance_sumSorted') }}: </span>
              <strong>{{ yeti.sumSorted|currencySymbol(currencySymbol) }}</strong>
            </td>
          </tr>
          <tr class="table-summary">
            <td
              :colspan="4"
            >
              <span>
                {{ $t('finance_balance') }}:
              </span>
              <strong
                v-if="yeti.balance !== 0"
                class="deep-orange--text text--darken-3"
              >
                {{ yeti.balance|currencySymbol(currencySymbol) }}
              </strong>
              <strong v-else>
                {{ yeti.balance|currencySymbol(currencySymbol) }}
              </strong>
            </td>
          </tr>
          <tr>
            <td :colspan="2">
              <v-icon
                small
                class="mr-2"
              >
                mdi-alert-rhombus-outline
              </v-icon>
              <span>{{ $t('finance_emergencyDrops') }}</span>
            </td>
            <td
              :colspan="2"
              class="text-right"
            >
              {{ yeti.emergencyDrops }}
            </td>
          </tr>
          <tr>
            <td :colspan="2">
              <v-icon
                small
                class="mr-2"
              >
                mdi-help-circle
              </v-icon>
              <span>{{ $t('finance_unrecognized') }}</span>
            </td>
            <td
              :colspan="2"
              class="text-right"
            >
              {{ yeti.unrecognized }}
            </td>
          </tr>
          <tr>
            <td :colspan="2">
              <v-icon
                small
                class="mr-2"
              >
                mdi-alpha-t-circle
              </v-icon>
              <span>{{ $t('turnover.table.tokens') }}</span>
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              colspan="2"
              :value="yeti.token"
              align="right"
            />
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';

export default {
  name: 'MoneyCollectYetiDetailsTable',
  components: {
    ExportAsyncModal,
    CustomCurrencySymbolCell,
  },
  props: {
    currencySymbol: {
      type: String,
      default: undefined,
    },
    parent: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      headers: [
        {
          value: 'coin',
          text: this.$t('turnover.table.coins'),
          align: 'center',
        },
        {
          value: 'hopperA',
          text: this.$t('turnover.table.hopperA'),
          align: 'center',
        },
        {
          value: 'hopperB',
          text: this.$t('turnover.table.hopperB'),
          align: 'center',
        },
        {
          value: 'safe',
          text: this.$t('finance_safe'),
          align: 'center',
        },
      ],
    };
  },
  computed: {
    items() {
      return [this.parent];
    },
    exportAsyncParams() {
      return {
        id: this.parent.moneyCollectId,
        report: 'v2\\FinanceMoneyCollects',
        sources: 'YETI',
      };
    },
  },
};
</script>
