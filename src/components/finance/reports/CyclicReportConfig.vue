<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('finance_listHeading') }}</span>
        </h2>
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-refresh
          class="mr-2"
          @click="getData"
        />
      </v-col>
      <v-col
        cols="12"
      >
        <v-data-table
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :options.sync="pagination"
          :loading="loaders.site"
          :server-items-length="dataTable.totalItems"
          :footer-props="dataTable.footerProps"
        >
          <template #[`item.ctime`]="{ item }">
            {{ item.ctime|formatDateDayTime }}
          </template>
          <template #[`item.period`]="{ item }">
            {{ $t(`common_frequency_${item.period}`) }}
          </template>
          <template #[`item.actions`]="{ item }">
            <v-layout
              class="d-flex justify-end"
            >
              <v-btn
                :key="`editReportCyclicConfigButton${item.id}`"
                x-small
                tile
                rounded
                fab
                class="ml-2"
                color="primary"
                elevation="1"
                @click="openModal(
                  `editReportCyclicConfigDialog${item.id}`,
                  { configId: item.id }
                )"
              >
                <v-icon>mdi-pencil</v-icon>
              </v-btn>
              <generate-cyclic-report-modal
                :key="`cyclicReportGenerateModal${item.id}`"
                :config-id="item.id"
                @cyclic-report-generate="propageteReportGenerate()"
              />
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <span v-on="on">
                    <v-btn
                      x-small
                      tile
                      rounded
                      fab
                      elevation="1"
                      color="red"
                      class="ml-2 white--text"
                      @click.stop="openDeleteDialog(item.id)"
                    >
                      <v-icon>mdi-delete</v-icon>
                    </v-btn>
                  </span>
                </template>
                <span>
                  {{ $t('actions.delete') }}
                </span>
              </v-tooltip>
            </v-layout>
            <cyclic-report-edit-modal
              :key="`editReportCyclicConfigDialog${item.id}`"
              :ref="`editReportCyclicConfigDialog${item.id}`"
              :config-id="item.id"
              @cyclic-config-edited="getData()"
            />
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <v-row justify="center">
      <v-dialog
        v-model="deleteDialog"
        max-width="500"
      >
        <v-card>
          <v-card-title class="text-h5">
            {{ $t("cyclicReport_deleteQuestion") }}
          </v-card-title>

          <v-card-actions>
            <v-spacer />
            <v-btn
              text
              color="primary"
              @click="deleteDialog = false"
            >
              {{ $t('actions.cancel') }}
            </v-btn>
            <v-btn
              color="red"
              class="white--text text-center"
              @click="deleteConfigAndCloseDialog()"
            >
              {{ $t('actions.delete') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-row>
  </v-container>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import CyclicReportEditModal from '@components/finance/reports/CyclicReportEditModal.vue';
import GenerateCyclicReportModal from '@components/finance/reports/GenerateCyclicReportModal.vue';
import snackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  components: {
    BtnRefresh,
    CyclicReportEditModal,
    GenerateCyclicReportModal,
  },
  mixins: [
    snackbarMixin,
  ],
  data() {
    return {
      configToDeleteId: null,
      deleteDialog: false,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['ctime'],
        sortDesc: [true],
      },
      dataTable: {
        totalItems: 0,
        items: [],
        footerProps: {
          'items-per-page-options': [10, 25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('finance_createTime'),
            value: 'ctime',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('finance_reportName'),
            value: 'title',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('finance_extension'),
            value: 'ext',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_frequency'),
            value: 'period',
            showInRowExpand: true,
            sortable: false,
          },
          {
            value: 'actions',
            text: this.$t('actions.actions'),
            showInRowExpand: true,
            sortable: false,
            align: 'end',
          },
        ],
      },
      count: 0,
      loaders: {
        site: false,
      },
    };
  },
  watch: {
    filtering: {
      handler() {
        this.pagination.page = 1;
        this.getData();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.itemsPerPage !== newValue.itemsPerPage) {
          this.pagination.page = 1;
        }
        this.getData();
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    openDeleteDialog(configToDeleteId) {
      this.configToDeleteId = configToDeleteId;
      this.deleteDialog = true;
    },
    deleteConfigAndCloseDialog() {
      this.deleteConfig(this.configToDeleteId);
      this.deleteDialog = false;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    propageteReportGenerate() {
      this.$emit('cyclic-report-generate');
    },
    deleteConfig(configId) {
      this.loaders.site = true;
      this.axios.delete(
        `/api/report_config/${configId}`,
      )
        .then((response) => {
          if (response.status === 200) {
            this.showSnackbar(
              'success',
              this.$t('common_success'),
            );
            this.getData();
          }
        })
        .catch(() => {
          this.showSnackbar(
            'error',
            this.$t('common_errorHeader'),
          );
        });
    },
    getData() {
      this.loaders.site = true;
      this.axios.get(
        '/api/report_configs',
        {
          params: {
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
          },
        },
      )
        .then(
          (response) => {
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = response.data.total;

            this.loaders.site = false;
          },
        );
    },
  },
};
</script>
