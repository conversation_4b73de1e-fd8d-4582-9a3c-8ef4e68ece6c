<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on }">
        <v-btn
          class="ml-2"
          x-small
          tile
          rounded
          fab
          elevation="1"
          color="secondary"
          v-on="on"
          @click.stop
          @click.native="showModal"
        >
          <v-icon>mdi-cloud-download</v-icon>
        </v-btn>
      </template>
      <span>{{ $t('common_exportAsyncTitle') }}</span>
    </v-tooltip>
    <v-dialog
      v-model="show"
      max-width="800"
    >
      <v-card>
        <v-card-title class="title">
          <slot name="title">
            <span class="headline text-uppercase text-h5">{{ $t('common_exportAsyncTitle') }}</span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click.native="close"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <div
            v-if="loading"
            class="text-center py-4"
          >
            <v-progress-circular
              class="circleProgress md-2"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
            <h3 class="mt-4">
              {{ $t('common_generating') }}
            </h3>
            <h4 class="mt-4">
              {{ $t('common_reportDownloadOnList') }}:
              <v-btn
                text
                color="primary"
                @click="$router.push('/finance/reports')"
              >
                {{ $t('common_reports') }}
              </v-btn>
            </h4>
          </div>
          <div
            v-if="!loading"
            class="mt-7"
          >
            <template
              v-if="errorText !== null"
            >
              <v-alert
                border="left"
                class="mt-5"
                text
                type="error"
              >
                {{ errorText }}
              </v-alert>
            </template>
            <template
              v-if="!showDownload"
            >
              <div
                class="text-center py-4"
              >
                <v-btn
                  color="primary"
                  @click="generateReport"
                >
                  <v-icon>
                    mdi-cached
                  </v-icon>
                  {{ $t('common_exportAsyncTitle') }}
                </v-btn>
              </div>
            </template>
            <div
              v-else
              class="text-center py-4"
            >
              <h3 class="py-4">
                {{ $t('common_reportReady') }}
              </h3>
              <v-btn
                class="mx-2"
                color="primary"
                @click.native="downloadReport"
              >
                {{ $t('common_downloadReport') }}
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'ExportAsyncModal',
  mixins: [
    ExportMixin,
    SnackbarMixin,
  ],
  props: {
    configId: {
      type: Number,
      default: null,
    },
    btnClass: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      reportEmail: {
        model: [],
        items: [],
        search: null,
      },
      rules: {
        comboboxEmail: [(v) => /(((^(([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))(,{1}((([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))))*)|(^$))$/.test(v) || 'E-mail must be valid'],
      },
      loading: false,
      errorText: null,
      resourceId: null,
      resourceStatus: null,
      intervalId: null,
      fileType: null,
      fileName: null,
      show: false,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
    }),
    showDownload() {
      return this.resourceStatus === 'DONE';
    },
  },
  mounted() {
    this.reportEmail.model = [
      this.user.email,
    ];
  },
  beforeDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  methods: {
    generateReport() {
      this.resourceStatus = 'initialized';
      this.loading = true;

      let reportEmail = this.reportEmail.model.join(',');
      if (reportEmail === '') {
        reportEmail = null;
      }

      this.axios.post(
        `/api/report_config/${this.configId}/generate`,
      )
        .then((response) => {
          this.fileName = response.data.fileName;
          this.fileType = response.data.ext;
          this.resourceId = response.data.id;
          this.propagateUpdate();
          this.checkResourceStatus(this.resourceId);
        })
        .catch(() => {
          this.errorText = `${this.$t('common_canotGenerateReport')}`;

          this.resourceStatus = 'Błąd podczas generowania zasobu.';
          this.loading = false;
        });
    },
    propagateUpdate() {
      this.$emit('cyclic-report-generate');
    },
    downloadReport() {
      const url = `/api/report/${this.resourceId}/download`;
      const filename = this.fileName;
      this.onExport({
        url,
        filename,
      });
    },
    checkResourceStatus(resourceId) {
      this.intervalId = setInterval(() => {
        this.axios.get(`/api/report/${resourceId}`)
          .then((response) => {
            this.resourceStatus = response.data.status;
            if (response.status === 200) {
              this.fileName = response.data.fileName;
              clearInterval(this.intervalId);
              this.propagateUpdate();
              this.loading = false;
            }
          })
          .catch(() => {
            this.errorText = `${this.$t('common_canotGenerateReport')}`;
            this.loading = false;
            clearInterval(this.intervalId);
            // this.close();
          });
      }, 1000); // Odpytywanie co 1 sekunde
    },
    showModal() {
      this.show = true;
      this.generateReport();
    },
    clearReportGenerator() {
      clearInterval(this.intervalId);
      this.resourceId = null;
      this.resourceStatus = null;
      this.intervalId = null;
      this.errorText = null;
      this.loading = false;
    },
    close() {
      this.show = false;
      this.clearReportGenerator();
    },
  },
};
</script>
