<template>
  <v-row>
    <v-col
      cols="12"
      class="mt-5"
    >
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <heading
            :dates="filtering.dates"
          />
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="ml-2"
            @click="getData"
          />
          <export-async-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
            :disabled="loader"
            :preset="filtering.dates.value"
          />
        </v-col>
      </v-layout>
    </v-col>
    <v-col
      cols="12"
      class="pt-0"
    >
      <fiscal-summary-table
        :loader="loader"
        :items="items"
        :page-sums="pageSums"
        :options="filtering.options"
        :currency-symbol="currencySymbol"
        @change="onFiltersChange"
      />
    </v-col>
  </v-row>
</template>

<script>
import Heading from '@components/finance/fiscal-summary/Heading.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import FiscalSummaryTable from '@components/finance/fiscal-summary/FiscalSummaryTable.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DateOptionsMixins from '@components/common/mixins/DateOptionsMixins.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import { endOfToday, startOfDay, subDays } from 'date-fns';

export default {
  name: 'FiscalSummary',
  components: {
    ExportAsyncModal,
    Heading,
    FiscalSummaryTable,
    BtnRefresh,
  },
  mixins: [
    DateOptionsMixins,
    SettingsMixin,
    DataFetchMixin,
    FilterMixin,
    FiltersHandlingMixin,
  ],
  props: {
    dates: {
      type: Object,
      default: () => ({}),
    },
    carwash: {
      type: [Object, Number],
      default: null,
      nullable: true,
    },
    fiscalStatus: {
      type: String,
      default: null,
      nullable: true,
    },
  },
  data() {
    return {
      curentDate: 'last7Days',
      first: true,
      defaultDates: {
        value: 'last7Days',
        text: this.$t('common_p7d'),
        from: startOfDay(subDays(new Date(), 6)),
        to: endOfToday(),
      },
      dataUrl: '/api/reports/data',
      filtering: {
        fiscal: null,
        options: {
          sortBy: ['date'],
          sortDesc: [true],
        },
      },
    };
  },
  computed: {
    currentDate: {
      get() {
        return this.curentDate;
      },
      set(newValue) {
        this.curentDate = newValue;
      },
    },
    exportAsyncParams() {
      const carwash = this.filtering.carwash || null;
      return {
        serial: carwash,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        report: 'v2\\FinanceFiscalSummary',
        fiscal: this.filtering.fiscal,
      };
    },
    currencySymbol() {
      return this.currencyObject.symbol;
    },
  },
  watch: {
    dates(val) {
      this.$set(this.filtering, 'dates', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    carwash(val) {
      this.$set(this.filtering, 'carwash', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    fiscalStatus(val) {
      this.$set(this.filtering, 'fiscal', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
  },
  mounted() {
    if (this.dates !== null) {
      this.$set(this.filtering, 'carwash', this.carwash);
      this.$set(this.filtering, 'dates', this.dates);
      this.$set(this.filtering, 'fiscal', this.fiscalStatus);
      this.getData();
    }
  },
  methods: {
    onFiltersChangePageReset(filters) {
      this.onFiltersChange(filters);
      // debouncing and close all lists
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row, index) => ({
        ...row,
        id: index,
        carwashName: row.carwashName,
        dates: this.filtering.dates,
      }));
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash || null,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          order: this.filtering.options.sortDesc[0] ? 'DESC' : 'ASC',
          fiscal: this.filtering.fiscal,
          report: 'v2\\FinanceFiscalSummary',
        },
      };
    },
  },
};
</script>
