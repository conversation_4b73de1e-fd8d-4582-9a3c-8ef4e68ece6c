<template>
  <div>
    <v-col cols="12">
      <type-filter
        :disabled="loader"
        @change="onFiltersChange"
      />
    </v-col>
    <v-col
      v-show="!hasErrors"
      cols="12"
    >
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <heading
            :dates="filtering.dates"
            :carwash="filtering.carwash"
          />
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="mr-2"
            :disabled="loader"
            @click="getData"
          />
          <export-async-modal
            btn-class="ml-2"
            :preset="filtering.dates.value"
            :params="exportAsyncParams"
            :disabled="loader"
          />
        </v-col>
      </v-layout>
    </v-col>
    <div
      v-if="!hasErrors"
    >
      <v-col
        cols="12"
        class="pt-3"
      >
        <fiscal-table
          :loader="loader"
          :items="items"
          :date-format-func="formatDate"
          :currency-symbol="currencySymbol"
        />
      </v-col>
    </div>
  </div>
</template>

<script>
import Heading from '@components/finance/fiscal-daily/daily/Heading.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import FiscalTable from '@components/finance/fiscal-daily/daily/FiscalTable.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import DataFetchMixin from '@components/finance/fiscal-daily/mixins/DataFetchMixin.vue';
import ExportMixin from '@components/finance/fiscal-daily/mixins/ExportMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import TypeFilter from '@components/common/filters/TypeFilter.vue';

export default {
  name: 'FiscalDaily',
  components: {
    TypeFilter,
    ExportAsyncModal,
    BtnRefresh,
    FiscalTable,
    Heading,
  },
  mixins: [
    FiltersHandlingMixin,
    DataFetchMixin,
    ExportMixin,
    FilterMixin,
  ],
  props: {
    dates: {
      type: Object,
      default: () => ({}),
    },
    carwash: {
      type: [Object, Number],
      default: null,
      nullable: true,
    },
    fiscalStatus: {
      type: String,
      default: null,
      nullable: true,
    },
  },
  data() {
    return {
      settingsNamespace: 'finance:dates',
      filtering: {
        dataSource: {
          value: '',
          text: '',
          dataUrl: '/api/reports/data',
          dateFormatFunc: this.$options.filters.formatDateDay,
        },
      },
    };
  },
  computed: {
    currencySymbol() {
      return this.currencyObject.symbol;
    },
    exportAsyncParams() {
      return {
        serial: this.filtering.carwash || null,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        report: 'v2\\FinanceFiscalByDay',
        type: this.filtering.grouping || 'daily',
        fiscal: this.filtering.fiscal || 'fiscalized',
      };
    },
  },
  watch: {
    dates(val) {
      this.$set(this.filtering, 'dates', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    carwash(val) {
      this.$set(this.filtering, 'carwash', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    fiscalStatus(val) {
      this.$set(this.filtering, 'fiscal', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    filtering: {
      handler(newValue, oldValue) {
        if (
          newValue.dates.from !== oldValue.dates.from
          || newValue.carwash !== oldValue.carwash
        ) {
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    if (this.dates !== null) {
      this.$set(this.filtering, 'carwash', this.carwash);
      this.$set(this.filtering, 'dates', this.dates);
      this.$set(this.filtering, 'fiscal', this.fiscalStatus);
      this.getData();
    }
  },
  methods: {
    formatDate(data) {
      if (this.filtering.grouping === 'monthly') {
        return this.$options.filters.formatDateMonth(data);
      }

      return this.$options.filters.formatDateDay(data);
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash || null,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          report: 'v2\\FinanceFiscalByDay',
          type: this.filtering.grouping || 'daily',
          fiscal: this.filtering.fiscal,
        },
      };
    },
  },
};
</script>
