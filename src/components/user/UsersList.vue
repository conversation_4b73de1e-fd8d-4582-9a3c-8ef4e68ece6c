<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        md="6"
        sm="6"
        cols="12"
      >
        <text-search
          v-model="search"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        sm="12"
        class="text-sm-start"
      >
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <h2>
              <span>{{ $t('loyalApp_usersList') }}</span>
            </h2>
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mr-2"
              @click="getData"
            />
            <export-async-modal
              btn-class="ml-2"
              :params="exportAsyncParams"
              :disabled="loader"
              :show-dates="false"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
      >
        <v-data-table
          item-key="id"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          :loading="loader"
          :footer-props="footerProps"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
        >
          <template #progress>
            <div class="text-center mx-n4">
              <v-progress-linear
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>
          <template #item="{ item }">
            <tr>
              <td class="text-sm-start">
                {{ item.firstname }}
              </td>
              <td class="text-sm-start border-right">
                {{ item.lastname }}
              </td>
              <td class="hidden-sm-and-down md-and-up text-sm-start">
                {{ item.email }}
              </td>
              <td class="hidden-sm-and-down md-and-up text-sm-start border-right">
                {{ item.phone ?? '-' }}
              </td>
              <td class="hidden-sm-and-down md-and-up text-sm-center border-right">
                <template v-if="item.isOwner =='true'">
                  <v-icon color="green">
                    mdi-check
                  </v-icon>
                </template>
                <template v-else>
                  <v-icon color="orange">
                    mdi-close
                  </v-icon>
                </template>
              </td>
              <td class="hidden-sm-and-down md-and-up text-sm-start border-right">
                {{ item.lastLogin|formatDateDayTime }}
              </td>
              <td class="text-sm-end">
                <div class="card-buttons">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-btn
                        :disabled="!canAccess('users', 'write')"
                        class="ml-2 card-button"
                        x-small
                        tile
                        fab
                        elevation="1"
                        color="primary"
                        v-bind="attrs"
                        v-on="on"
                        @click.stop="openModal(
                          `editUserDialog${item.id}`,
                          {userNumber: item.id, email: item.email}
                        )"
                      >
                        <v-icon>mdi-pencil</v-icon>
                      </v-btn>
                    </template>
                    <span>{{ $t('actions.edit') }}</span>
                  </v-tooltip>

                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-btn
                        :disabled="!canAccess('users', 'write')"
                        class="ml-2 card-button"
                        x-small
                        tile
                        rounded
                        fab
                        elevation="1"
                        color="primary"
                        v-bind="attrs"
                        v-on="on"
                        @click.stop="openModal(
                          `deleteUserDialog${item.id}`,
                          { item }
                        )"
                      >
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                    <span>{{ $t('actions.delete') }}</span>
                  </v-tooltip>
                </div>
              </td>
            </tr>
            <user-add-edit-modal
              :key="`editUserDialog${item.id}`"
              :ref="`editUserDialog${item.id}`"
              :user-number="item.id"
              :email="item.email"
              action="edit"
              @update-user-list="getData()"
            />
            <user-delete-modal
              :key="`deleteUserDialog${item.id}`"
              :ref="`deleteUserDialog${item.id}`"
              :user-number="item.id"
              @update-user-list="getData()"
            />
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import debounce from 'lodash/debounce';
import UserAddEditModal from '@components/user/UserAddEditModal.vue';
import UserDeleteModal from '@components/user/UserDeleteModal.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    ExportAsyncModal,
    BtnRefresh,
    TextSearch,
    UserAddEditModal,
    UserDeleteModal,
  },
  data() {
    return {
      search: '',
      loader: false,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['id'],
        sortDesc: [true],
      },
      footerProps: {
        'items-per-page-options': [10, 25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      dataTable: {
        items: [],
        totalItems: -1,
        headers: [
          {
            text: this.$t('common_firstName'),
            value: 'firstname',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('common_lastName'),
            value: 'lastname',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            class: 'hidden-sm-and-down md-and-up text-sm-start',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('common_phone'),
            value: 'phone',
            class: 'hidden-sm-and-down md-and-up text-sm-start',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('admin_isOwner'),
            value: 'isOwner',
            class: 'hidden-sm-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('common_lastLogin'),
            value: 'lastLogin',
            class: 'hidden-sm-and-down md-and-up text-sm-start',
            showInRowExpand: true,
            displayMethod: 'date',
            sortable: true,
          },
          {
            text: '',
            align: 'right',
            sortable: false,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    exportAsyncParams() {
      return {
        report: 'v2\\UserReport',
      };
    },
  },
  watch: {
    search: {
      handler() {
        this.pagination.page = 1;
        this.getDataDebounced();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
            || oldValue.page !== newValue.page
            || oldValue.itemsPerPage !== newValue.itemsPerPage
            || oldValue.sortBy !== newValue.sortBy)
          && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getDataDebounced();
        }
      },
      deep: true,
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getData();
    }, 1000);
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.autoLoadData = true;
      this.loader = true;
      this.axios.get(
        '/api/users',
        {
          params: {
            search: this.search,
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = Number(response.data.total);
            this.loader = false;
          }
        })
        .catch((error) => {
          if (error.response && error.response.status === 402) {
            this.loader = false;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>
