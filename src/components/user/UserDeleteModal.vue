<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      persisten
      content-class="dialogWidth-2"
      style="z-index: 1300"
      width="50%"
    >
      <v-card>
        <v-card-text class="pt-8 card-text-wrap">
          <div class="text-center">
            <v-icon class="p-6 red--text fa-4x">
              mdi-alert
            </v-icon>
            <p>{{ $t('user_hint') }}</p>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            text
            color="primary"
            :disabled="loaders.userDelete"
            @click.native="dialog = false"
          >
            {{ $t('actions.cancel') }}
          </v-btn>
          <v-btn
            color="red"
            class="white--text text-center"
            :loading="loaders.userDelete"
            @click.native="userDelete"
          >
            {{ $t('actions.delete') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>

export default {
  props: {
    userNumber: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      loaders: {
        userDelete: false,
      },
      buttons: {
        user: true,
      },
      dialog: false,
    };
  },
  methods: {
    onError() {
      // on error
      this.toggleButtons(true);
      this.closeDialog();
    },
    userDelete() {
      this.loaders.userDelete = true;
      this.axios.delete(
        `/api/user/${this.userNumber}`,
      )
        .then(
          () => {
            this.propagateUserUpdate();
            this.closeDialog();
          },
        );
      this.loaders.userDelete = false;
    },
    propagateUserUpdate() {
      this.$emit('update-user-list');
    },
    closeDialog() {
      this.dialog = false;
    },
    toggleButtons(value) {
      this.buttons.user = value;
    },
    disableButtonsExcept(name) {
      this.toggleButtons(false);
      this.buttons[name] = true;
    },
  },
};
</script>

<style scoped>
.card-text-wrap {
  display: flex;
  flex-direction: column;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}
</style>
