<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        class="px-0"
      >
        <v-row>
          <v-col
            cols="4"
            sm="4"
          >
            <text-search
              v-model="text"
              @input="getDataDebounced"
            />
          </v-col>
          <v-col
            cols="4"
            sm="4"
          >
            <multiselect
              v-model="filtering.status"
              :items="statusesDisabled"
              :label="filters.statuses.text"
              :prepend-icon="filters.statuses.icon"
              :return-array="true"
              :disabled="loader"
              unified
              dense
              allow-null
            />
          </v-col>
          <v-col
            cols="4"
            xs="12"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mt-0"
              size="small"
              :disabled="loader"
              @click="getData"
            />
            <export-async-modal
              btn-class="mt-2 ml-2"
              :params="getParams().params"
              :disabled="loader"
              :show-dates="false"
            />
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="12"
        class="px-0"
      >
        <v-data-table
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :options.sync="pagination"
          :loading="loader"
          :footer-props="dataTable.footerProps"
          :server-items-length="dataTable.totalItems"
        >
          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start pl-5">
                  {{ item.id }}
                </td>
                <td class="text-sm-start">
                  {{ item.userEmail }}
                </td>
                <td class="text-sm-start">
                  {{ item.fleetEmail }}
                </td>
                <td class="text-sm-start">
                  {{ item.startTime|formatDateDayTime }}
                </td>
                <td class="text-sm-start">
                  {{ item.endTime|formatDateDayTime }}
                </td>
                <td class="text-sm-start">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-icon
                        :color="getStatusDetails(item.status).color"
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{ getStatusDetails(item.status).icon }}
                      </v-icon>
                    </template>
                    <span> {{ getStatusDetails(item.status).text }}</span>
                  </v-tooltip>
                </td>
                <td class="text-sm-right">
                  {{ item.value|currencySymbol(item.currencySymbol) }}
                </td>
                <td class="text-sm-right">
                  {{ item.left|currencySymbol(item.currencySymbol) }}
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import debounce from 'lodash/debounce';
import TextSearch from '@components/common/filters/TextSearch.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';

export default {
  components: {
    ExportAsyncModal,
    TextSearch,
    Multiselect,
    BtnRefresh,
  },
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loader: true,
      filters: {
        statuses: {
          icons: {
            error: 'mdi-close-octagon-outline',
            confirmed: 'mdi-check-circle-outline',
            rejected: 'mdi-alert-outline',
            initiated: 'mdi-cached',
            timeout: 'mdi-clock-outline',
            waiting: 'mdi-cached',
            refunded: 'mdi-credit-card-refund-outline',
          },
          colors: {
            error: 'error',
            confirmed: 'green darken-2',
            rejected: 'error',
            initiated: 'progress',
            timeout: 'error',
            waiting: 'progress',
            refunded: 'warning',
          },
          texts: {
            error: this.$t('common_error'),
            confirmed: this.$t('common_confirmed'),
            rejected: this.$t('common_canceled'),
            initiated: this.$t('common_initiated'),
            timeout: this.$t('common_timeout'),
            waiting: this.$t('common_processing'),
            refunded: this.$t('common_refund'),
          },
          text: this.$t('common_state'),
          icon: 'mdi-list-status',
        },
      },
      filtering: {
        status: [
        ],
        search: {
          text: '',
        },
      },
      text: null,
      pagination: {
        sortBy: ['id'],
        sortDesc: [true],
        page: 1,
        itemsPerPage: 25,
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [10, 25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_user'),
            value: 'userEmail',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_fleet'),
            value: 'fleetEmail',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyaltyApp_startTime'),
            value: 'startTime',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyaltyApp_endTime'),
            value: 'endTime',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('common_state'),
            value: 'status',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_value'),
            value: 'value',
            class: 'text-sm-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_left'),
            value: 'left',
            class: 'text-sm-end',
            showInRowExpand: true,
            sortable: false,
          },
        ],
        items: [],
      },
      possibleStatuses: [],
    };
  },
  computed: {
    statusesDisabled() {
      if (this.possibleStatuses !== undefined) {
        return this.possibleStatuses.map(
          (row) => ({
            text: this.filters.statuses.texts[row.name] ?? row.name,
            value: row.name,
            disabled: false,
            icon: this.filters.statuses.icons[row.name] ?? '',
          }),
        );
      }

      return [];
    },
  },
  watch: {
    app() {
      this.getData();
    },
    filtering: {
      handler() {
        this.pagination.page = 1;
        this.getData();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
          || oldValue.sortBy !== newValue.sortBy) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
            this.getData(true);
            return;
          }
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.pagination.page = 1;
      this.getData();
    }, 1000);
  },
  methods: {
    getStatusDetails(status) {
      if (status in this.filters.statuses.icons) {
        return {
          icon: this.filters.statuses.icons[status],
          color: this.filters.statuses.colors[status],
          text: this.filters.statuses.texts[status],
        };
      }

      return {
        icon: this.filters.statuses.icons.default,
        color: this.filters.statuses.colors.default,
        text: status,
      };
    },
    getToggleStatusFiltering() {
      return this.filtering.status.length ? this.filtering.status.join(',') : null;
    },
    // onResize() {
    //   this.windowWidth = window.innerWidth;
    // },
    getParams() {
      return {
        params: {
          report: 'v2\\WlaSubscriptionPackagesReport',
          app: this.app,
          search: this.text ?? null,
          status: this.getToggleStatusFiltering(),
        },
      };
    },
    getData() {
      this.loader = true;
      this.dataTable.items = [];
      this.dataTable.totalItems = 0;
      this.axios.get(
        '/api/loyalapp/subscription-packages',
        {
          params: {
            ...this.getParams().params,
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
          },
        },
      )
        .then((response) => {
          this.dataTable.totalItems = response.data.total;
          this.dataTable.items = response.data.data;
          this.possibleStatuses = response.data.filters.statuses;
          this.loader = false;
        });
    },
  },
};
</script>
