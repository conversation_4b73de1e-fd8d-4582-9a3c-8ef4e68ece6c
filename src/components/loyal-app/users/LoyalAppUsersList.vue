<template>
  <v-container
    fluid
  >
    <v-col
      cols="12"
      class="pt-0 px-0"
    >
      <v-card-text class="px-0 pb-0">
        <v-layout
          row
          wrap
        >
          <v-col
            md="6"
            sm="6"
            cols="12"
            class="py-0"
          >
            <text-search
              :disabled="loader"
              @change="onFiltersChangePageReset"
            />
          </v-col>
          <v-col
            md="6"
            sm="6"
            cols="12"
            class="px-0 py-0 pt-1"
          >
            <date-range-picker
              key="dateRange"
              ref="dateRange"
              prepend-icon="mdi-calendar-range"
              :show-presets="true"
              start-preset="currentMonth"
              :show-custom="true"
              :disabled="loader"
              @reload-transaction-list="onDateRangeChange"
            />
          </v-col>
        </v-layout>
      </v-card-text>
    </v-col>
    <v-col
      cols="12"
      class="px-0"
    >
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          md="8"
          class="text-sm-start"
        >
          <h2>
            <span>{{ $t('loyalApp_usersListHeading') }}</span>
          </h2>
        </v-col>
        <v-col
          cols="12"
          md="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="mt-0 mr-1"
            size="small"
            :disabled="loader"
            @click="getData"
          />
          <v-btn
            small
            color="primary"
            class="mr-2 mt-1"
            @click.native="openModal('LoyalAppUserInviteModal')"
          >
            <v-icon left>
              mdi-plus
            </v-icon>
            {{ $t('actions.invite_user') }}
          </v-btn>
          <notification-modal
            :url="`/api/gateway/wla-admin/users-test/send?app=${app}`"
            @success="message => showSnackbar('success', message)"
            @error="message => showSnackbar('error', message)"
          />
          <export-async-modal
            btn-class="ml-2 mt-1"
            :params="exportAsyncParams"
            :disabled="loader"
            :show-dates="false"
          />
        </v-col>
      </v-layout>
    </v-col>
    <v-row>
      <v-col
        cols="12"
        class="px-0"
      >
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :footer-props="footerProps"
          :server-items-length="dataTable.totalItems"
          :single-expand="true"
        >
          <template #item="{ item, expand, isExpanded }">
            <template v-if="!loader">
              <tr
                :key="item.id"
                :class="getRowWarningClass(item)"
                @click="expand(!isExpanded)"
              >
                <td class="text-sm-start">
                  {{ item.id }}
                </td>
                <td class="md-and-up text-sm-start font-weight-bold">
                  <v-tooltip
                    v-if="item.alerts && item.alerts.length"
                    right
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-icon class="mr-2">
                          mdi-alert
                        </v-icon>
                        {{ item.email }}
                      </div>
                    </template>
                    <span>{{ $t('loyalApp_loyalappExpandDetailsToSeeAlerts') }}</span>
                  </v-tooltip>
                  <template v-else>
                    {{ item.email }}
                  </template>
                </td>
                <td>
                  {{ item.managerEmail ?? '-' }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  {{ item.ctime|formatDateDayTime }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  {{ item.lastUsage|formatDateDayTime }}
                </td>
                <td
                  class="hidden-sm-and-down md-and-up text-sm-center"
                >
                  <v-icon
                    v-if="item.fleetManager"
                  >
                    mdi-check
                  </v-icon>
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-center">
                  <v-icon
                    v-if="item.trustedPartner"
                  >
                    mdi-check
                  </v-icon>
                </td>
                <td class="hidden-xs-only md-and-up text-sm-end border-right">
                  <v-tooltip
                    v-if="item.fleetMember"
                    left
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{ $t('loyalApp_balanceFleetMemberNotify') }}
                      </div>
                    </template>
                    <span>{{ $t('loyalApp_balanceFleetMemberTooltip') }}</span>
                  </v-tooltip>
                  <template v-else>
                    {{ item.balance|currencySymbol(item.currencySymbol) }}
                  </template>
                </td>
                <td class="text-sm-end">
                  <v-col
                    class="hidden-sm-and-down d-flex justify-end"
                  >
                    <loyal-app-user-edit-modal
                      :user-id="item.id"
                      :app="app"
                      @reload="getData"
                    />
                    <user-top-up-modal
                      :app="app"
                      :user-id="item.id"
                      :user-email="item.email"
                      :currency-symbol="item.currencySymbol"
                      :trusted-partner="item.trustedPartner"
                      @reload="getData"
                    />
                    <notification-modal
                      v-if="item.notificable"
                      :url="`/api/gateway/wla-admin/user/${item.id}/send?app=${app}`"
                      @success="message => showSnackbar('success', message)"
                      @error="message => showSnackbar('error', message)"
                    />
                  </v-col>
                </td>
                <td
                  class="text-end"
                >
                  <v-icon v-if="isExpanded">
                    mdi-chevron-up
                  </v-icon>
                  <v-icon v-else>
                    mdi-chevron-down
                  </v-icon>
                </td>
              </tr>
            </template>
          </template>
          <template #expanded-item="{ headers, item }">
            <td
              v-if="!loader"
              :colspan="headers.length"
            >
              <single-user-table
                :key="`user-details-${item.id}`"
                :app="app"
                :user-id="item.id"
              />
            </td>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <loyal-app-user-invite-modal
      ref="LoyalAppUserInviteModal"
      :app="app"
    />
  </v-container>
</template>

<script>
import LoyalAppUserEditModal from '@components/loyal-app/users/LoyalAppUserEditModal.vue';
import LoyalAppUserInviteModal from '@components/loyal-app/users/LoyalAppUserInviteModal.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import debounce from 'lodash/debounce';
import UserTopUpModal from '@components/loyal-app/users/UserTopUpModal.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import SingleUserTable from '@components/loyal-app/users/SingleUserTable.vue';
import NotificationModal from '@components/admin/NotificationModal.vue';

export default {
  components: {
    ExportAsyncModal,
    UserTopUpModal,
    SingleUserTable,
    TextSearch,
    DateRangePicker,
    LoyalAppUserEditModal,
    LoyalAppUserInviteModal,
    BtnRefresh,
    NotificationModal,
  },
  mixins: [
    SnackbarMixin,
    FiltersHandlingMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loader: true,
      filtering: {
        search: {
          text: null,
          client: null,
        },
        dates: {
          from: null,
          to: null,
        },
      },
      footerProps: {
        'items-per-page-options': [10, 25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['last_usage'],
        sortDesc: [false],
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('loyalApp_managerEmail'),
            value: 'manager_email',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_tableCreateDate'),
            value: 'ctime',
            showInRowExpand: true,
            displayMethod: 'date',
            sortable: true,
            class: 'hidden-sm-and-down md-and-up',
          },
          {
            text: this.$t('common_lastUsage'),
            value: 'last_usage',
            showInRowExpand: true,
            displayMethod: 'date',
            sortable: true,
            class: 'hidden-sm-and-down md-and-up',
          },
          {
            text: this.$t('loyalApp_fleetManager'),
            value: 'fleet_manager',
            showInRowExpand: true,
            align: 'center',
            sortable: false,
            class: 'hidden-sm-and-down md-and-up',
          },
          {
            text: this.$t('loyalApp_tableTrustedPartner'),
            value: 'trusted_partner',
            showInRowExpand: true,
            align: 'center',
            sortable: false,
            class: 'hidden-sm-and-down md-and-up',
          },
          {
            text: `${this.$t('loyalApp_balance')}`,
            value: 'balance',
            showInRowExpand: true,
            align: 'right',
            displayMethod: 'currency',
            sortable: true,
            class: 'hidden-xs-only md-and-up',
          },
          {
            text: this.$t('actions.actions'),
            value: 'actions',
            align: 'right',
            sortable: false,
          },
          {
            text: '',
            value: 'id',
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
      },
    };
  },
  computed: {
    noData() {
      return this.loader ? '' : this.$t('common_noData');
    },
    exportAsyncParams() {
      return {
        report: 'v2\\WlaUsersReport',
        ...this.getParams(),
      };
    },
  },
  watch: {
    app() {
      if (this.app != null) {
        this.getDataDebounced();
      }
    },
    filtering: {
      handler() {
        this.getDataDebounced();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
          || oldValue.sortBy !== newValue.sortBy) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    filters: {
      handler() {
        this.getDataDebounced();
      },
      deep: true,
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getData();
    }, 800);
  },
  mounted() {
  },
  methods: {
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getParams() {
      return {
        orderBy: this.pagination.sortBy[0],
        orderDescending: (this.pagination.sortDesc[0] === true) ? 1 : 0,
        search: this.filtering.search.text,
        startDate: this.filtering.dates.from,
        endDate: this.filtering.dates.to,
        app: this.app,
      };
    },
    getData() {
      if (this.app == null) return;

      this.loader = true;
      this.axios.get(
        '/api/loyalapp/users',
        {
          params: {
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
            ...this.getParams(),
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data !== null) {
            this.dataTable.totalItems = response.data.total;
            this.dataTable.items = response.data.data;
            this.loader = false;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].openDialog();
    },
    onDateRangeChange(dates) {
      this.$set(this.filtering, 'dates', dates);
    },
    onFiltersChangePageReset(filters) {
      this.pagination.page = 1;
      this.onFiltersChange({
        search: {
          text: filters.search,
        },
      });
    },
    getRowWarningClass(user) {
      if (user.alerts && user.alerts.length) {
        const found = user.alerts.find((element) => element.level !== 'info');

        if (found !== undefined) {
          return 'warning';
        }
      }
      return '';
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.expand-table {
  width: 100%
}

.warning {
  color: rgba(0, 0, 0, 0.57) !important;
  background-color: rgb(245, 166, 35) !important;
  border-color: rgb(245, 166, 35) !important;
}
</style>
