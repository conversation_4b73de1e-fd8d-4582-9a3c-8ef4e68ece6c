<template>
  <v-data-table
    dense
    item-key="id"
    mobile-breakpoint="0"
    :headers="headers"
    :items="tableItems"
    :loading="loader"
    :server-items-length="itemsTotal"
    :sort-desc="true"
    :options.sync="filtering.options.pagination"
    :footer-props="footerProps"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item }">
      <template v-if="!loader">
        <tr
          :key="item.id"
          class="text-sm-start"
        >
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.createdAt|formatDateDayTime }}
          </td>
          <td class="text-start tabcell-carwash">
            {{ item.serviceDate|formatDateDay }}
          </td>
          <td class="text-start no-wrap">
            {{ item.number }}
          </td>
          <td class="text-start">
            {{ item.clientName }}
          </td>
          <td class="text-start">
            {{ item.clientVatId }}
          </td>
          <td class="text-start tabcell-carwash">
            {{ item.sendDate|formatDateDayTime }}
          </td>
          <custom-currency-symbol-cell
            :value="parseFloat(item.priceNet)"
            :currency="item.currency"
          />
          <custom-currency-symbol-cell
            :value="parseFloat(item.price)"
            :currency="item.currency"
          />
          <td class="text-end">
            <v-col
              class="d-flex justify-end pt-1 pb-1"
            >
              <btn-download2
                :url="`/api/gateway/bkfpay-owner/invoice/${item.id}/download`"
              />
              <top-up-send-invoice-modal
                v-if="item.clientId !== null"
                :invoice-id="item.id"
                x-small
                :client-id="item.clientId"
              />
            </v-col>
          </td>
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import TopUpSendInvoiceModal from '@components/loyalty-cards/TopUpSendInvoiceModal.vue';
import BtnDownload2 from '@components/common/BtnDownload2.vue';

export default {
  name: 'InvoicesTable',
  components: {
    BtnDownload2,
    CustomCurrencySymbolCell,
    TopUpSendInvoiceModal,
  },
  mixins: [
    FilterMixin,
    ExportMixin,
  ],
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    sums: {
      type: Object,
      required: true,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filtering: {
        options: {
          pagination: {
            page: 1,
            itemsPerPage: 10,
            sortBy: ['mtime'],
            sortDesc: [true],
          },
        },
      },
      footerProps: {
        'items-per-page-options': [5, 10, 15, 25],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'createdAt',
          text: this.$t('loyaltyCards_issuanceDate'),
        },
        {
          value: 'serviceDate',
          text: this.$t('loyaltyCards_serviceDate'),
        },
        {
          value: 'number',
          text: this.$t('loyaltyCards_invoiceNumber'),
          align: 'start',
        },
        {
          value: 'clientName',
          text: this.$t('loyaltyCards_client'),
          align: 'start',
        },
        {
          value: 'clientVatId',
          text: this.$t('table.tax_number'),
          align: 'start',
        },
        {
          value: 'sendDate',
          text: this.$t('invoices.send-date'),
        },
        {
          value: 'priceNet',
          text: this.$t('loyaltyCards_valueNet'),
          align: 'end',
        },
        {
          value: 'price',
          text: this.$t('loyaltyCards_valueGross'),
          align: 'end',
        },
        {
          value: 'downloadUrl',
          text: ' ',
          sortable: false,
          align: 'end',
          width: '130px',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }

      return this.items;
    },
  },
  methods: {
  },
};
</script>

<style>

.no-wrap {
  white-space: nowrap;
}

</style>
