<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <loading-overlay v-if="loaders.site" />
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <v-alert
          v-if="!isOwner"
          text
          border="left"
          type="info"
        >
          {{ $t('loyaltyCards_notOwnerAlert') }}
        </v-alert>
      </v-col>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('loyaltyCards_invoiceSettings') }}</span>
        </h2>
      </v-col>
      <v-col
        cols="12"
        class="px-0 pt-0"
      >
        <v-form
          ref="formUserEdit"
          v-model="form.valid"
          :disabled="!isOwner || !isPremiumSubscription"
          lazy-validation
        >
          <v-layout wrap>
            <v-col
              cols="12"
              class="pb-0"
            >
              <v-col
                align="left"
                sm="12"
                class="py-0 px-0"
              >
                <v-alert
                  border="left"
                  text
                  :color="(isOwner && isPremiumSubscription)
                    ? 'primary darken-1'
                    : 'primary lighten-1'"
                >
                  <strong>{n}</strong> - {{ $t('loyaltyCards_invoiceNumerator') }} <br>
                  <strong>{M}</strong> - {{ $t('loyaltyCards_longMonthFormat') }} <br>
                  <strong>{m}</strong> - {{ $t('loyaltyCards_shortMonthFormat') }} <br>
                  <strong>{Y}</strong> - {{ $t('loyaltyCards_longYearFormat') }} <br>
                  <strong>{y}</strong> - {{ $t('loyaltyCards_shortYearFormat') }} <br>
                </v-alert>
              </v-col>
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="invoiceData.numeratorFormat"
                v-validate="'required|min:1'"
                :label="$t('loyaltyCards_invoiceNumerator')"
                prepend-icon="mdi-label-outline"
                name="numeratorFormat"
                :data-vv-as="`${$t('loyaltyCards_invoiceNumerator')}`"
                :error-messages="errors.collect('numeratorFormat')"
                required
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="py-0"
            >
              <v-autocomplete
                v-model="invoiceData.vatTax.id"
                v-validate="'required|min:1'"
                item-value="id"
                item-text="taxKey"
                :label="$t('loyaltyCards_vatTax')"
                :items="vatTaxOptions"
                :autocomplete="true"
                prepend-icon="mdi-bank-outline"
                name="vatTax"
                :data-vv-as="`${$t('loyaltyCards_vatTax')}`"
                :error-messages="errors.collect('vatTax')"
                :rules="rules.selectRequired"
                required
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-autocomplete
                v-model="invoiceData.paymentTerm"
                v-validate="'required|min:1'"
                item-value="id"
                :label="$t('common_paymentPeriod')"
                :items="paymentTermOptions"
                :autocomplete="true"
                prepend-icon="mdi-update"
                name="paymentPeriodType"
                :data-vv-as="`${$t('common_paymentPeriod')}`"
                :error-messages="errors.collect('paymentPeriodType')"
                :rules="rules.selectRequired"
                required
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="invoiceData.additionalInfo"
                :label="$t('loyaltyCards_additionalInfo')"
                prepend-icon="mdi-label-outline"
                name="additionalInfo"
                :data-vv-as="`${$t('loyaltyCards_additionalInfo')}`"
                :error-messages="errors.collect('additionalInfo')"
              />
            </v-col>
            <v-col
              cols="12"
              align="right"
              class="pt-0 pb-0"
            >
              <v-btn
                color="primary darken-1"
                :loading="loaders.actualize"
                :disabled="!form.valid || !isOwner || !isPremiumSubscription"
                @click.native="submit"
              >
                {{ $t('actions.save') }}
              </v-btn>
            </v-col>
          </v-layout>
        </v-form>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import LoadingOverlay from '@components/common/LoadingOverlay.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    LoadingOverlay,
  },
  data() {
    return {
      rules: {
        fileSize: [(value) => !value || value.size < 500000 || 'Avatar size should be less than 0,5 MB!'],
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
      },
      vatTaxOptions: null,
      paymentTermOptions: [
        {
          id: 'P0D',
          text: this.$t('common_0'),
        },
        {
          id: 'P1D',
          text: this.$t('common_1'),
        },
        {
          id: 'P3D',
          text: this.$t('common_3'),
        },
        {
          id: 'P5D',
          text: this.$t('common_5'),
        },
        {
          id: 'P7D',
          text: this.$t('common_7'),
        },
        {
          id: 'P10D',
          text: this.$t('common_10'),
        },
        {
          id: 'P14D',
          text: this.$t('common_14'),
        },
      ],
      loaders: {
        site: true,
        actualize: false,
      },
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      invoiceData: {
        vatTax: {
          id: 30,
        },
        numeratorFormat: '{n}/{Y}/{m}',
        paymentTerm: 'P1D',
        additionalInfo: null,
      },
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isPremiumSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_PREMIUM');
    },
  },
  mounted() {
    this.getData();
    this.getVatTax();
  },
  methods: {
    getVatTax() {
      this.axios.get(
        '/api/lists/vat_tax',
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.vatTaxOptions = response.data;
          }
        });
    },
    getData() {
      this.loaders.site = true;
      this.axios.get(
        '/api/loyalty/config',
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.invoiceData.vatTax.id = response.data.vatTax.id;
            this.invoiceData.numeratorFormat = response.data.numeratorFormat;
            this.invoiceData.paymentTerm = response.data.paymentTerm;
            this.invoiceData.additionalInfo = response.data.additionalInfo;
          }
          this.loaders.site = false;
        });
      this.loaders.site = false;
    },
    submit() {
      if (this.isOwner && this.isPremiumSubscription) {
        this.loaders.actualize = true;
        this.loaders.site = true;
        this.axios.patch(
          '/api/loyalty/config',
          {
            ...this.invoiceData,
          },
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loaders.actualize = false;
                this.loaders.site = false;
              }
            },
            () => {
              this.loaders.actualize = false;
              this.loaders.site = false;
              // this.onError();
            },
          );
      }
    },
  },
};
</script>
