<template>
  <v-row justify="center">
    <v-dialog
      v-model="dialog"
      fullscreen
      hide-overlay
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ cardTitle }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="dialog = false"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <loyalty-transactions-list
            :ref="`transactionsListModal${card.number}`"
            :auto-update-transactions="false"
            :autoload-data="false"
            :card-number="card.number"
            :card-token="card.token"
            :show-filtering="false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>

import LoyaltyTransactionsList from './LoyaltyTransactionsList.vue';

export default {
  components: {
    LoyaltyTransactionsList,
  },
  props: {
    cardNumber: {
      type: String,
      default: null,
    },
    cardToken: {
      type: String,
      default: null,
    },
    cardName: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      card: {
        number: this.cardNumber,
        token: this.cardToken,
        name: this.cardName,
      },
      dialog: false,
    };
  },
  computed: {
    cardTitle() {
      if (this.card.name) {
        return `${this.$t('transactions.history_for_card')} - ${this.card.number} (${this.card.name})`;
      }
      return `${this.$t('transactions.history_for_card')} - ${this.card.number}`;
    },
  },
  watch: {
    dialog() {
      // get card data only when dialog shows up
      if (this.dialog) {
        setTimeout(() => {
          this.getTransactionsData();
        }, 1000);
      }
    },
  },
  methods: {
    getTransactionsData() {
      this.$refs[`transactionsListModal${this.card.number}`].getData();
    },
  },
};
</script>
