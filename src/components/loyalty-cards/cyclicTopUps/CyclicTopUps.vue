<template>
  <div>
    <v-layout
      row
      wrap
    >
      <v-col cols="12" />
      <v-col
        class="pt-0"
        cols="12"
      >
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="4"
          >
            <text-search
              v-model="search"
              @input="getDataDebounced"
            />
          </v-col>
          <v-col
            offset-sm="5"
            cols="7"
            sm="3"
          />
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <heading
              :dates="filtering.dates"
            />
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mr-2"
              @click="getData"
            />
            <export-async-modal
              btn-class="ml-2"
              :show-dates="false"
              :params="exportAsyncParams"
              :disabled="loader"
              :preset="filtering.dates.value"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="pt-0"
      >
        <cyclic-top-ups-table
          :loader="loader"
          :items="tableItems"
          :items-total="totalItems"
          :sums="sums"
          :options="filtering.options"
          @change="onFiltersChange"
          @reload-cyclic-confi-list="getData"
        />
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import debounce from 'lodash/debounce';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';
import Heading from './Heading.vue';
import CyclicTopUpsTable from './CyclicTopUpsTable.vue';

export default {
  name: 'CyclicTopUpList',
  components: {
    CyclicTopUpsTable,
    Heading,
    BtnRefresh,
    // DateRangePicker,
    TextSearch,
    ExportAsyncModal,
  },
  mixins: [
    FiltersHandlingMixin,
    DataFetchMixin,
    FilterMixin,
  ],
  data() {
    return {
      exportItems: [
        {
          title: 'actions.export_csv',
          action: 'exportCsv',
          icon: 'get_app',
        },
        {
          title: 'actions.export_xlsx',
          action: 'exportXlsx',
          icon: 'description',
        },
      ],
      paymentDateFilter: false,
      search: null,
      itemsTotal: 0,
      dataUrl: '/api/loyalty/v3/cyclic_top_ups',
      filtering: {
        options: {
          sortBy: ['date'],
          sortDesc: [true],
          itemsPerPage: 25,
        },
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyCyclicTopUps3Report',
        search: this.search,
      };
    },
    tableItems() {
      if (this.loader) {
        return [];
      }

      return this.items;
    },
  },
  mounted() {
    this.getData();
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.filtering.options.page = 1;
      this.getData();
    }, 1000);
  },
  methods: {
    searchText(value) {
      if (value != null && this.search === null) {
        return true;
      }

      return value.indexOf(this.search) !== -1;
    },
    dateBetween(value) {
      if ((this.dateFrom === null && this.dateTo === null)) {
        return true;
      }

      return value > this.dateFrom && value < this.dateTo;
    },
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row) => ({
        ...row,
      }));
    },
    getParams() {
      return {
        params: {
          order: this.filtering.options.sortDesc[0] ? 'DESC' : 'ASC',
          page: this.filtering.options.page || null,
          perPage: this.filtering.options.itemsPerPage || null,
          search: this.search,
        },
      };
    },
  },
};
</script>
