<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search
          v-model="filtering.search.text"
          :rules="searchRule"
        />
      </v-col>
      <v-col>
        <multiselect
          v-model="filtering.virtual"
          :items="filters.virtual.options"
          :label="filters.virtual.text"
          :prepend-icon="filters.virtual.icon"
          :disabled="loader"
          unified
          allow-null
        />
      </v-col>
      <v-col class="d-flex">
        <v-switch
          v-model="showUnusedCardsCheckbox"
          :disabled="loader"
          hide-details
          class="mr-2"
          @change="getData"
        />
        <v-col class="py-0">
          <date-range-picker
            key="dateRange"
            ref="dateRange"
            show-presets
            prepend-icon="mdi-calendar-range"
            :disabled="!showUnusedCardsCheckbox || loader"
            format="YYYY-MM-DD"
            :label="$t('loyaltyCards_usedInPeriod')"
            @reload-transaction-list="onDateRangeChange"
          />
        </v-col>
      </v-col>
    </v-row>
    <v-row class="mt-0">
      <v-col class="py-0">
        <multiselect
          v-model="filtering.status"
          :items="filters.status.options"
          :label="filters.status.text"
          :prepend-icon="filters.status.icon"
          unified
          allow-null
        />
      </v-col>
      <v-col class="py-0">
        <multiselect
          v-model="filtering.foundsExists"
          :items="filters.founds.options"
          :label="filters.founds.text"
          :prepend-icon="filters.founds.icon"
          unified
          allow-null
        />
      </v-col>
      <v-col class="py-0">
        <multiselect
          v-model="filtering.nameExists"
          :items="filters.name.options"
          :label="filters.name.text"
          :prepend-icon="filters.name.icon"
          unified
          allow-null
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        sm="6"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('loyaltyCards_cardsList') }}</span>
        </h2>
      </v-col>
      <v-col
        class="hidden-sm-and-down d-flex justify-end"
      >
        <btn-refresh
          @click="getData"
        />
        <v-btn
          class="ml-2"
          small
          color="primary"
          @click.native="openModal('addMultipleCardsDialog')"
        >
          <v-icon left>
            mdi-playlist-plus
          </v-icon>
          {{ $t('actions.add_multiple_key') }}
        </v-btn>
        <v-btn
          class="ml-2"
          small
          color="primary"
          @click.native="openModal('addCardDialog')"
        >
          <v-icon left>
            mdi-plus
          </v-icon>
          {{ $t('actions.add_key') }}
        </v-btn>
        <export-async-modal
          btn-class="ml-2"
          :show-dates="showUnusedCardsCheckbox"
          :params="exportAsyncParams"
          :disabled="loader"
          :preset="filtering.search.dateValue"
        />
      </v-col>
    </v-row>
    <v-row v-if="!isDataCompleted">
      <v-col>
        <v-alert
          :value="true"
          border="left"
          type="error"
        >
          <div class="info-content">
            <span class="text">{{ topUpResult }}</span>
          </div>
        </v-alert>
      </v-col>
    </v-row>
    <v-row v-if="isSuccess">
      <v-col>
        <v-alert
          :value="true"
          :icon="successIcon"
          border="left"
          type="success"
        >
          <div class="info-content">
            <span class="text">{{ successMessage }}</span>
          </div>
        </v-alert>
      </v-col>
    </v-row>
    <v-row class="mt-0">
      <v-col class="px-0">
        <v-data-table
          v-resize="onResize"
          :headers="filteredColumns"
          :items="dataTable.items"
          item-key="number"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="dataTable.footerProps"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-linear
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item, }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start">
                  <div class="card-container mt-2">
                    <div class="card-number">
                      {{ item.number.toUpperCase() }}
                    </div>
                    <v-tooltip
                      :key="`notice${item.number}`"
                      bottom
                      class="notice-icon"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-icon
                            v-if="item.fullComment != ''"
                            color="yellow darken-2"
                          >
                            mdi-alert
                          </v-icon>
                        </div>
                      </template>
                      <span>{{ item.fullComment }}</span>
                    </v-tooltip>
                    <v-tooltip
                      :key="`virtualLock${item.number}`"
                      bottom
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          :key="`virtualLockIcon${item.number}`"
                          class="notice-icon"
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-icon
                            v-if="item.status === 'BLOCKED'"
                          >
                            mdi-lock
                          </v-icon>
                        </div>
                      </template>
                      <span>{{ $t('common_cardBlocked') }}</span>
                    </v-tooltip>
                    <v-tooltip
                      :key="`virtual${item.number}`"
                      bottom
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          :key="`virtualIcon${item.number}`"
                          class="notice-icon"
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-icon
                            v-if="item.type === 'VIRTUAL'"
                          >
                            mdi-web
                          </v-icon>
                        </div>
                      </template>
                      <span>{{ $t('loyaltyCards_tableVirtualCard') }}</span>
                    </v-tooltip>
                    <p class="hidden-md-and-up m-0 cardName card-name">
                      {{ item.alias }}
                    </p>
                  </div>
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  {{ item.alias }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  {{ item.email }}
                </td>
                <td
                  v-if="canAccess('loyalty', 'clients')"
                  class="hidden-sm-and-down md-and-up text-sm-start"
                >
                  {{ item.clientName ? item.clientName : '' }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start border-right">
                  <template v-if="item.lastContact !== null">
                    {{ item.lastContact|formatDateDayTime }}
                  </template>
                  <template v-else>
                    &mdash;
                  </template>
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-end">
                  {{ item.topUps|currencySymbol(item.currencySymbol) }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-end">
                  {{ item.payments|currencySymbol(item.currencySymbol) }}
                </td>
                <td class="text-sm-end border-right">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <div
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{ item.balance|currencySymbol(item.currencySymbol) }}
                      </div>
                      <div v-if="item.toSend && item.toSend > 0">
                        (+{{ item.toSend|currencySymbol(item.currencySymbol) }}
                      </div>
                    </template>
                    <span>{{ $t('loyaltyCards_cardFundsTooltip') }}</span>
                  </v-tooltip>
                </td>
                <td class="text-end align-center justify-end">
                  <div class="card-buttons">
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-btn
                          :key="item.number"
                          tile
                          rounded
                          x-small
                          fab
                          color="primary"
                          class="card-button mr-2"
                          elevation="1"
                          v-bind="attrs"
                          v-on="on"
                          @click.stop
                          @click.native="openModal(`cardTopUpHistoryDialog${item.number}`)"
                        >
                          <v-icon>mdi-receipt</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('transactions.topup_card_history') }}</span>
                    </v-tooltip>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-btn
                          :key="item.number"
                          class="card-button mr-2"
                          elevation="1"
                          v-bind="attrs"
                          tile
                          rounded
                          x-small
                          fab
                          color="primary"
                          v-on="on"
                          @click.stop
                          @click.native="openModal(`cardTransactionsDialog${item.number}`)"
                        >
                          <v-icon>mdi-history</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('transactions.history_for_card') }}</span>
                    </v-tooltip>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-btn
                          :key="item.number"
                          class="card-button mr-2"
                          elevation="1"
                          v-bind="attrs"
                          tile
                          rounded
                          x-small
                          fab
                          color="secondary"
                          v-on="on"
                          @click.stop
                          @click.native="openModal(
                            `editCardDialog${item.number}`,
                            {cardNumber: item.number}
                          )"
                        >
                          <v-icon>mdi-pencil</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('actions.edit') }}</span>
                    </v-tooltip>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-btn
                          :key="item.number"
                          class="card-button"
                          elevation="1"
                          v-bind="attrs"
                          tile
                          rounded
                          x-small
                          fab
                          color="primary"
                          v-on="on"
                          @click.stop
                          @click.native="openModal(
                            `topUpCardDialog${item.number}`,
                            {cardNumber: item.number}
                          )"
                        >
                          <v-icon>mdi-trending-up</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('actions.refill_card') }}</span>
                    </v-tooltip>
                  </div>
                  <div style="height:0px;width:0px">
                    <card-top-up-modal
                      :key="`topUpCardDialog${item.number}`"
                      :ref="`topUpCardDialog${item.number}`"
                      :card-number="item.number"
                      :card-token="item.cardToken"
                      :client-id="item.clientId ? item.clientId : null"
                      @reload-card-list="getDataDebounced"
                    />
                    <card-edit-modal
                      :key="`editCardButton${item.number}`"
                      :ref="`editCardDialog${item.number}`"
                      :card-number="item.number"
                      :card-token="item.cardToken"
                      @reload-card-list="getDataDebounced"
                    />
                    <card-transactions-modal
                      :key="`cardTransactionsButton${item.number}`"
                      :ref="`cardTransactionsDialog${item.number}`"
                      :card-number="item.number"
                      :card-token="item.cardToken"
                      :card-name="item.alias"
                    />
                    <card-top-ups-history-modal
                      :key="`cardTopUpHistoryButton${item.number}`"
                      :ref="`cardTopUpHistoryDialog${item.number}`"
                      :card-number="item.number"
                      :card-token="item.cardToken"
                      :card-name="item.alias"
                    />
                  </div>
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>

    <card-add-modal
      ref="addCardDialog"
      @reload-card-list="getData"
    />
    <card-multiple-add-modal
      ref="addMultipleCardsDialog"
      @reload-card-list="getData"
    />
  </v-container>
</template>

<script>
import formatDate from 'date-fns/format';
import CardAddModal from '@components/loyalty-cards/CardAddModal.vue';
import CardMultipleAddModal from '@components/loyalty-cards/CardMultipleAddModal.vue';
import CardEditModal from '@components/loyalty-cards/CardEditModal.vue';
import CardTopUpModal from '@components/loyalty-cards/CardTopUpModal.vue';
import CardTransactionsModal from '@components/loyalty-cards/CardTransactionsModal.vue';
import CardTopUpsHistoryModal from '@components/loyalty-cards/CardTopUpsHistoryModal.vue';
import moment from 'moment-timezone';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import debounce from 'lodash/debounce';
import { mapGetters } from 'vuex';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';

export default {
  components: {
    ExportAsyncModal,
    Multiselect,
    BtnRefresh,
    TextSearch,
    CardMultipleAddModal,
    CardAddModal,
    CardEditModal,
    CardTopUpModal,
    CardTransactionsModal,
    DateRangePicker,
    CardTopUpsHistoryModal,
  },
  mixins: [
    SnackbarMixin,
    ExportMixin,
  ],
  data() {
    return {
      singleExpand: true,
      expanded: [],
      searchRule: [
        (value) => /^((?!%|\?|!|\)|\(|'|\\|\/|&).)*$/.test(value) || this.$t(
          'form.validation.invalid_value',
        ),
      ],
      exportItems: [
        {
          title: 'actions.export_csv',
          action: 'exportCsv',
          icon: 'get_app',
        },
        {
          title: 'actions.export_xlsx',
          action: 'exportXlsx',
          icon: 'description',
        },
      ],
      loader: true,
      isDataCompleted: true,
      isSuccess: false,
      topUpResult: '???',
      isOverlay: false,
      successMessage: '',
      successIcon: '',
      showUnusedCardsCheckbox: false,
      advanced: false,
      exportMenu: [
        {
          text: this.$t('actions.export_summary'),
          action: 'exportSummary',
          icon: 'get_app',
        },
      ],
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['lastContact'],
        sortDesc: [true],
      },
      filtering: {
        virtual: null,
        search: {
          text: null,
          client: parseInt(this.$route.params.client_id, 10)
            ? parseInt(this.$route.params.client_id, 10) : null,
          dateFrom: null,
          dateTo: null,
          dateValue: null,
        },
        status: null,
        foundsExists: null,
        nameExists: null,
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [25, 50, 100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        items: [],
        totalItems: 0,
      },
      filters: {
        name: {
          text: this.$t('loyaltyCards_names'),
          icon: 'mdi-format-text',
          options: [
            {
              text: this.$t('loyaltyCards_withNames'),
              value: 1,
              icon: 'mdi-format-title',
            },
            {
              text: this.$t('loyaltyCards_withoutNames'),
              value: 0,
              icon: 'mdi-format-strikethrough',
            },
          ],
        },
        virtual: {
          text: this.$t('loyaltyCards_cardType'),
          icon: 'mdi-web',
          options: [
            {
              text: this.$t('loyaltyCards_virtual'),
              value: 1,
              icon: 'mdi-web',
            },
            {
              text: this.$t('loyaltyCards_regular'),
              value: 0,
              icon: 'mdi-credit-card-outline',
            },
          ],
        },
        status: {
          text: this.$t('loyaltyCards_activity'),
          icon: 'mdi-credit-card-settings-outline',
          options: [
            {
              text: this.$t('loyaltyCards_filtersActive'),
              value: 'ACTIVE',
              icon: 'mdi-lock-open',
            },
            {
              text: this.$t('loyaltyCards_blocked'),
              value: 'LOCKED',
              icon: 'mdi-lock',
            },
          ],
        },
        founds: {
          text: this.$t('loyaltyCards_funds'),
          icon: 'mdi-wallet',
          options: [
            {
              text: this.$t('loyaltyCards_withFounds'),
              value: 1,
              icon: 'mdi-currency-usd',
            },
            {
              text: this.$t('loyaltyCards_withoutFounds'),
              value: 0,
              icon: 'mdi-currency-usd-off',
            },
          ],
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyCards3Report',
        // order: this.pagination.sortBy[0],
        // desc: this.pagination.sortDesc[0],
        search: this.filtering.search.text,
        status: this.filtering.status,
        foundsExists: this.filtering.foundsExists,
        nameExists: this.filtering.nameExists,
        isVirtual: this.filtering.virtual,
      };
    },
    headers() {
      return [
        {
          text: this.$t('loyaltyCards_card'),
          value: 'number',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_name'),
          value: 'alias',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('common_email'),
          value: 'email',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('common_client'),
          value: 'clientId',
          class: 'hidden-sm-and-down md-and-up',
          show: this.canAccess('loyalty', 'clients'),
          sortable: false,
        },
        {
          text: this.$t('common_lastUsage'),
          value: 'mtime',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('common_topups'),
          value: 'topUps',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          align: 'right',
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_payments'),
          value: 'payments',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          align: 'right',
          sortable: false,
        },
        {
          text: `${this.$t('loyaltyCards_cardFunds')} (${this.$t('loyaltyCards_toSend')})`,
          value: 'balance',
          align: 'right',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          align: 'right',
          sortable: false,
        },
      ];
    },
    filteredColumns() {
      return this.headers.filter((column) => column.show !== false);
    },
  },
  watch: {
    filtering: {
      handler(newValue) {
        if (newValue.search.text !== null) {
          const result = newValue.search.text.match(/^((?!%|\?|!|\)|\(|'|\\|\/|&).)*$/g);
          if (result !== null) {
            this.pagination.page = 1;
            this.getDataDebounced();
          }
        } else {
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
          || oldValue.sortBy !== newValue.sortBy) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
            this.getData(true);
            return;
          }
          this.getData();
        }
      },
      deep: true,
    },
    filters: {
      handler() {
        this.getDataDebounced();
      },
      deep: true,
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getData();
    }, 800);
  },
  async mounted() {
    // show one card when there is card in url
    if (this.$route.query.card) {
      this.filtering.search.text = this.$route.query.card;
    }
  },
  methods: {
    getDateInUTC(date) {
      return moment.utc(date)
        .toDate();
    },
    onDateRangeChange({
      from,
      to,
      value,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
      this.filtering.search.dateValue = value;
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    prependNewRow() {
      this.getData();
    },
    getData(resetPagination = false) {
      this.loader = true;
      if (resetPagination) {
        this.pagination.page = 1;
      }

      const params = {
        page: this.pagination.page,
        // order: this.pagination.sortBy[0],
        // desc: this.pagination.sortDesc[0],
        perPage: this.pagination.itemsPerPage,
        search: this.filtering.search.text,
        status: this.filtering.status,
        foundsExists: this.filtering.foundsExists,
        nameExists: this.filtering.nameExists,
        isVirtual: this.filtering.virtual,
      };

      if (this.showUnusedCardsCheckbox) {
        params.startDate = this.filtering.search.dateFrom;
        params.endDate = this.filtering.search.dateTo;
      }

      this.axios.get(
        '/api/loyalty/v3/cards',
        {
          params,
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = response.data.total;
            this.loader = false;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    getExportFileName(name, fileType) {
      const today = formatDate(new Date(), 'YYYY-MM-DD');
      const nameArray = [[name, today].join('_'), fileType];

      return nameArray.join('.');
    },
    toggleFilteringOption(groupIndex, optionIndex) {
      const inverted = !this.filters[groupIndex].options[optionIndex].active;
      this.filters[groupIndex].options[optionIndex].active = inverted;
    },
  },
};
</script>

<style lang="css" scoped>

.card-container {
  display: flex;
  grid-template-columns: auto 23px 23px;
  grid-template-rows: auto auto;
}

.card-container>.card-number {
  grid-column: 1;
  grid-row: 1;
  padding-top: 2px;
}

.card-container>.lock-icon {
  grid-column: 2;
  grid-row: 1;
}

.card-container>.notice-icon {
  grid-column: 2;
  grid-row: 1;
}

.card-container>.card-name {
  grid-column: 1 / 4;
  grid-row: 2;
}

.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.expand-table {
  width: 100%
}

.overlayDIV {
  position: fixed; /* Sit on top of the page content */
  display: block; /* Hidden by default */
  width: 100%; /* Full width (cover the whole page) */
  height: 100%; /* Full height (cover the whole page) */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Black background with opacity */
  cursor: pointer; /* Add a pointer on hover */
  z-index: 1000000 !important;
}

.overlayDIV span {
  padding: 5px;
  position: relative;
  top: 50%;
}

.card-buttons {
  display: flex;
  justify-content: flex-end;
}

.card-button {
  margin-right: 0.3rem;
}
</style>
