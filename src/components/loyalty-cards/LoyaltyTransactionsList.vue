<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <template v-if="showFiltering">
      <v-row>
        <v-col class="pb-0">
          <text-search
            v-model="filtering.search.text"
          />
        </v-col>
        <v-col>
          <multiselect
            v-model="filtering.virtual"
            :items="filters.virtual.options"
            :label="filters.virtual.text"
            :prepend-icon="filters.virtual.icon"
            :disabled="loader"
            unified
            allow-null
          />
        </v-col>
        <v-col>
          <v-autocomplete
            v-model="filtering.search.carwashSerialNumber"
            right
            :items="carwashesOptions"
            :label="$t('loyaltyCards_carwash')"
            class="input-group"
            item-value="serialNumber"
            item-text="longName"
            prepend-icon="mdi-car-wash"
            :disabled="loader"
          />
        </v-col>
        <v-col class="pb-0">
          <date-range-picker
            key="dateRange"
            ref="dateRange"
            prepend-icon="mdi-calendar-range"
            :show-presets="true"
            :show-custom="true"
            :disabled="loader"
            @reload-transaction-list="onDateRangeChange"
          />
        </v-col>
      </v-row>
      <v-row class="mt-0">
        <v-col>
          <multiselect
            v-model="filtering.nameExists"
            unified
            :items="withoutNames"
            prepend-icon="mdi-format-text"
            :label="$t('loyaltyCards_names')"
            :disabled="loader"
            allow-null
          />
        </v-col>
        <v-col>
          <multiselect
            v-model="filtering.transactionType"
            :items="typesDisabled"
            :label="filters.types.text"
            prepend-icon="mdi-cash-multiple"
            :return-array="true"
            :disabled="loader"
            unified
            allow-null
          />
        </v-col>
        <v-col>
          <multiselect
            v-model="filtering.transactionSource"
            :items="sourcesDisabled"
            :label="$t('loyaltyCards_source')"
            :return-array="true"
            :disabled="loader"
            prepend-icon="mdi-point-of-sale"
            unified
            allow-null
          />
        </v-col>
      </v-row>
    </template>
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start pb-0"
      >
        <h2>{{ $t('card.transactions_list') }}</h2>
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end pb-0"
      >
        <btn-refresh
          class="mr-2"
          @click="getDataDebounced()"
        />
        <export-async-modal
          btn-class="ml-2"
          :params="exportAsyncParams"
          :disabled="loader"
          :preset="filtering.search.dateValue"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col class="px-0">
        <v-data-table
          v-resize="onResize"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="transactionId"
          :loading="loader"
          :options.sync="pagination"
          :single-expand="singleExpand"
          :server-items-length="dataTable.totalItems"
          :expanded="expanded"
          :footer-props="dataTable.footerProps"
        >
          <template #progress>
            <div class="text-center mx-n4">
              <v-progress-linear
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start">
                  {{ item.time|formatDateDayTime }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  {{ item.carwashName }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  <device-type-badge
                    :device-type="item.source"
                    :stand-id="item.bayId"
                    color="grey lighten-1"
                    class="d-inline-block mr-1"
                    small
                  />
                </td>
                <td class="text-sm-start">
                  <div class="card-container mt-2">
                    <div class="card-number">
                      {{ item.cardNumber }}
                    </div>
                    <v-tooltip
                      :key="`virtual${item.cardNumber}`"
                      bottom
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          class="notice-icon"
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-icon
                            v-if="item.cardType === 'VIRTUAL'"
                          >
                            mdi-web
                          </v-icon>
                        </div>
                      </template>
                      <span>{{ $t('loyaltyCards_tableVirtualCard') }}</span>
                    </v-tooltip>
                    <p class="hidden-md-and-up m-0 cardName card-name">
                      {{ item.name }}
                    </p>
                  </div>
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  {{ item.cardAlias ?? '-' }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-icon
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{
                          getPaymentTypeDetails(item.type).icon
                        }}
                      </v-icon>
                    </template>
                    <span>{{
                      getPaymentTypeDetails(item.type).text
                    }}</span>
                  </v-tooltip>
                </td>
                <td class="text-sm-end">
                  {{ item.value|currencySymbol(item.currencySymbol) }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-end">
                  <template v-if="item.balance !== null">
                    {{ item.balance|currencySymbol(item.currencySymbol) }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import formatDate from 'date-fns/format';
import { subDays } from 'date-fns';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import { mapGetters } from 'vuex';
import debounce from 'lodash/debounce';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import DeviceTypeBadge from '@components/common/badge/DeviceTypeBadge.vue';
import TransactionTypeMixin from '@components/common/badge/icon-text-mixin/TransactionTypeMixin.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ExportAsyncModal from '@components/common/ExportAsyncModal.vue';

export default {
  components: {
    ExportAsyncModal,
    DeviceTypeBadge,
    BtnRefresh,
    TextSearch,
    Multiselect,
    DateRangePicker,
  },
  mixins: [
    ExportMixin,
    TransactionTypeMixin,
    SnackbarMixin,
  ],
  props: {
    showFiltering: {
      type: Boolean,
      default: true,
    },
    cardNumber: {
      type: String,
      default: null,
    },
    cardToken: {
      type: String,
      default: null,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    autoUpdateTransactions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      singleExpand: true,
      expanded: [],
      autoLoadData: this.autoLoad,
      registerUserNameEvent: this.autoUpdateTransactions,
      cardNum: this.cardNumber,
      cardTok: this.cardToken,
      loader: true,
      advanceFiltering: this.showFiltering,
      advanced: false,
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['orderBy'],
        sortDesc: [true],
      },
      filters: {
        virtual: {
          text: this.$t('loyaltyCards_cardType'),
          icon: 'mdi-web',
          options: [
            {
              text: this.$t('loyaltyCards_virtual'),
              value: 1,
              icon: 'mdi-web',
            },
            {
              text: this.$t('loyaltyCards_regular'),
              value: 0,
              icon: 'mdi-credit-card-outline',
            },
          ],
        },
        types: {
          icons: {
            ADDITION: 'mdi-trending-up',
            SUBTRACTION: 'mdi-trending-down',
            PROMOTION: 'mdi-sale',
            TOP_UP_CODE: 'mdi-card-plus',
            ALIGNMENT: 'mdi-wrench',
          },
          texts: {
            ADDITION: this.$t('transactions.topups'),
            SUBTRACTION: this.$t('transactions.payments'),
            PROMOTION: this.$t('transactions.promotions'),
            TOP_UP_CODE: this.$t('transactions.top-up-code'),
            ALIGNMENT: this.$t('transactions.balance_adjustment'),
          },
          text: this.$t('common_transactionType'),
          icon: 'mdi-trending-up',
        },
        source: {
          icons: {
            INTERNET: 'mdi-web',
            MONEY_CHANGER: 'mdi-cash-100',
            CAR_WASH: 'mdi-car-wash',
            VACUUM_CLEANER: 'mdi-auto-fix',
            DISTRIBUTOR: 'mdi-cup-water',
            UNKNOWN: 'mdi-help-circle',
          },
          texts: {
            INTERNET: this.$t('transactions.internet'),
            MONEY_CHANGER: this.$t('transactions.money_changer'),
            CAR_WASH: this.$t('transactions.car_wash'),
            VACUUM_CLEANER: this.$t('transactions.vacuum_cleaner'),
            DISTRIBUTOR: this.$t('transactions.distributor'),
            UNKNOWN: this.$t('transactions.unknown'),
          },
          text: this.$t('common_transactionType'),
          icon: 'mdi-trending-up',
        },
      },
      filtering: {
        virtual: null,
        search: {
          text: null,
          client: parseInt(this.$route.params.client_id, 10)
            ? parseInt(this.$route.params.client_id, 10) : null,
          carwashSerialNumber: null,
          dateFrom: null,
          dateTo: null,
          dateValue: null,
          hourFrom: null,
          hourTo: null,
        },
        transactionType: [
        ],
        transactionMethod: [
        ],
        transactionSource: [
        ],
        nameExists: null,
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [10, 15, 25],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_tableDate'),
            value: 'createdAt',
            showInRowExpand: true,
            displayMethod: 'date',
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_carwash'),
            value: 'carWashSerialNumber',
            class: 'hidden-sm-and-down md-and-up',
            displayMethod: 'carwash',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_stand'),
            value: 'carWashSerialNumber',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_card'),
            value: 'cardNumber',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_name'),
            value: 'cardName',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_type'),
            value: 'method',
            class: 'hidden-sm-and-down md-and-up',
            displayMethod: 'paymentMethod',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_value'),
            value: 'credits',
            class: 'text-sm-end',
            displayMethod: 'currency',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_valueAfterTransaction'),
            value: 'balanceAfterTransaction',
            class: 'hidden-sm-and-down md-and-up text-sm-end',
            displayMethod: 'currency',
            showInRowExpand: true,
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
      },
      possibleTypes: [],
      possibleSources: [],
      carwashesOptions: [],
      clientsOptions: [],
      transactionTypes: [
        {
          text: this.$t('transactions.topups'),
          value: 'TOP_UP',
          icon: 'mdi-trending-up',
        },
        {
          text: this.$t('transactions.payments'),
          value: 'PAYMENT',
          icon: 'mdi-trending-down',
        },
        {
          text: this.$t('transactions.balance_adjustment'),
          value: 'ALIGNMENT',
          icon: 'mdi-wrench',
        },
        {
          text: this.$t('transactions.promotions'),
          value: 'PROMOTION',
          icon: 'mdi-sale',
        },
      ],
      transactionMethods: [
        {
          text: this.$t('transactions.internet'),
          value: 'INTERNET',
          icon: 'mdi-web',
        },
        {
          text: this.$t('transactions.money_changer'),
          value: 'MONEY_CHANGER',
          icon: 'mdi-cash-100',
        },
        {
          text: this.$t('transactions.car_wash'),
          value: 'CAR_WASH',
          icon: 'mdi-car-wash',
        },
        {
          text: this.$t('transactions.vacuum_cleaner'),
          value: 'VACUUM_CLEANER',
          icon: 'mdi-auto-fix',
        },
        {
          text: this.$t('transactions.distributor'),
          value: 'DISTRIBUTOR',
          icon: 'mdi-cup-water',
        },
        {
          text: this.$t('transactions.unknown'),
          value: 'UNKNOWN',
          icon: 'mdi-help-circle',
        },
      ],
      withoutNames: [
        {
          text: this.$t('loyaltyCards_withNames'),
          value: 1,
          icon: 'mdi-format-title',
        },
        {
          text: this.$t('loyaltyCards_withoutNames'),
          value: 0,
          icon: 'mdi-format-strikethrough',
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
    typesDisabled() {
      const types = this.possibleTypes.map(
        (row) => ({
          text: this.filters.types.texts[row] ?? row,
          value: row,
          disabled: false,
          icon: this.filters.types.icons[row] ?? '',
        }),
      );
      return types;
    },
    sourcesDisabled() {
      const types = this.possibleSources.map(
        (row) => ({
          text: this.filters.source.texts[row] ?? row,
          value: row,
          disabled: false,
          icon: this.filters.source.icons[row] ?? '',
        }),
      );
      return types;
    },
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyTransactions3Report',
        // orderBy: this.pagination.sortBy[0],
        // desc: (this.pagination.sortDesc[0] === true) ? 1 : 0,
        type: this.getTransactionTypeFiltering(),
        source: this.getTransactionSourcesFiltering(),
        carWashSerialNumber: this.filtering.search.carwashSerialNumber,
        startDate: this.filtering.search.dateFrom && this.cardNum == null
          ? this.filtering.search.dateFrom
          : null,
        endDate: this.filtering.search.dateTo && this.cardNum == null
          ? this.filtering.search.dateTo
          : null,
        search: this.filtering.search.text,
        cardToken: this.cardTok,
        nameExists: this.filtering.nameExists,
        cardIsVirtual: this.filtering.virtual,
      };
    },
  },
  watch: {
    filtering: {
      handler() {
        if (this.autoLoadData) {
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
            || oldValue.page !== newValue.page
            || oldValue.itemsPerPage !== newValue.itemsPerPage
            || oldValue.sortBy !== newValue.sortBy)
          && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getData();
        }
      },
      deep: true,
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getData();
    }, 800);
  },
  mounted() {
    // show one card when there is card in url
    if (this.$route.query.card) {
      this.filtering.search.text = this.$route.query.card;
    }

    this.carwashesToMultiselectFormat();

    // when componend in modal don't load data automatically
    if (this.autoLoadData) {
      this.filtering.search.dateFrom = formatDate(subDays(new Date(), 7), 'YYYY-MM-DD');
      this.filtering.search.dateTo = formatDate(new Date(), 'YYYY-MM-DD');
      this.getDataDebounced();
    }
  },
  methods: {
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    carwashesToMultiselectFormat() {
      this.carwashesOptions = this.multiselectHelper.toSelect(this.carwashes);
      this.carwashesOptions.unshift({
        serialNumber: null,
        longName: this.$t('common_all'),
      });
    },
    getTransactionSourcesFiltering() {
      return this.filtering.transactionSource.length ? this.filtering.transactionSource.join(',') : null;
    },
    getTransactionTypeFiltering() {
      return this.filtering.transactionType.length ? this.filtering.transactionType.join(',') : null;
    },
    getData() {
      this.autoLoadData = true;
      this.loader = true;
      this.axios.get(
        '/api/loyalty/v3/transactions',
        {
          params: {
            page: this.pagination.page,
            // orderBy: this.pagination.sortBy[0],
            // desc: (this.pagination.sortDesc[0] === true) ? 1 : 0,
            perPage: this.pagination.itemsPerPage,
            type: this.getTransactionTypeFiltering(),
            source: this.getTransactionSourcesFiltering(),
            carWashSerialNumber: this.filtering.search.carwashSerialNumber,
            startDate: this.filtering.search.dateFrom && this.cardNum == null
              ? this.filtering.search.dateFrom
              : null,
            endDate: this.filtering.search.dateTo && this.cardNum == null
              ? this.filtering.search.dateTo
              : null,
            search: this.filtering.search.text,
            cardToken: this.cardTok,
            nameExists: this.filtering.nameExists,
            cardIsVirtual: this.filtering.virtual,
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.totalItems = response.data.total;
            this.dataTable.items = response.data.data;
            this.possibleTypes = response.data.filters.type;
            this.possibleSources = response.data.filters.source;

            this.loader = false;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    onDateRangeChange({
      from,
      to,
      value,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
      this.filtering.search.dateValue = value;
    },
    getExportFileName(name, fileType) {
      const today = formatDate(new Date(), 'YYYY-MM-DD');
      const nameArray = [[name, today].join('_'), fileType];

      return nameArray.join('.');
    },
  },
};
</script>

<style lang="css" scoped>

.card-container {
  display: grid;
  grid-template-rows: auto auto;
}

.card-container>.card-number {
  grid-column: 1;
  grid-row: 1;
  padding-top: 2px;
}

.card-container>.lock-icon {
  grid-column: 2;
  grid-row: 1;
}

.card-container>.notice-icon {
  grid-column: 2;
  grid-row: 1;
}

.card-container>.card-name {
  grid-column: 1 / 4;
  grid-row: 2;
}

.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.expand-table {
  width: 100%
}
</style>
